<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live Admin Navigation Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="http://localhost:8000/static/css/admin_navigation.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-xl font-bold mb-6">Live Admin Navigation Test</h1>
        
        <!-- Load actual admin navigation -->
        <div id="admin-nav-container">
            <div class="px-4 space-y-2">
                <!-- User Management -->
                <div class="nav-section" data-nav-section="users">
                    <a href="#" 
                       class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                       data-nav-item="users">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                        User Management
                        <div class="ml-auto bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">5</div>
                        <svg class="w-4 h-4 ml-2 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </a>
                    
                    <!-- User Management submenu -->
                    <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                        <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                            </svg>
                            View All Users
                        </a>
                        <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add User
                        </a>
                    </div>
                </div>

                <!-- System Settings -->
                <div class="nav-section" data-nav-section="settings">
                    <a href="#" 
                       class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                       data-nav-item="settings">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                        System Settings
                        <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </a>
                    
                    <!-- System Settings submenu -->
                    <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                        <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            General Settings
                        </a>
                        <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            Email Configuration
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div id="test-results" class="mt-6"></div>
        
        <!-- Test Buttons -->
        <div class="mt-4 space-x-2">
            <button id="test-dropdown" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Test Dropdown
            </button>
            <button id="manual-test" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                Manual Test
            </button>
        </div>
        
        <!-- Instructions -->
        <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
            <h3 class="font-bold text-yellow-800">Instructions:</h3>
            <p class="text-yellow-700 text-sm">
                1. Click "Test Dropdown" to run automated tests<br>
                2. Click "Manual Test" to manually test dropdowns<br>
                3. Try clicking on "User Management" and "System Settings" to see if dropdowns work
            </p>
        </div>
    </div>

    <!-- Load the actual admin navigation JavaScript -->
    <script src="http://localhost:8000/static/js/admin_navigation.js"></script>
    
    <script>
        // Test functionality
        document.getElementById('test-dropdown').addEventListener('click', function() {
            const results = [];
            
            // Test 1: Check if AdminNavigation class exists
            results.push({
                test: 'AdminNavigation Class',
                result: typeof AdminNavigation !== 'undefined',
                details: typeof AdminNavigation !== 'undefined' ? 'Class loaded successfully' : 'Class not found'
            });
            
            // Test 2: Check if navigation sections exist
            const navSections = document.querySelectorAll('.nav-section');
            results.push({
                test: 'Navigation Sections',
                result: navSections.length > 0,
                details: `Found ${navSections.length} sections`
            });
            
            // Test 3: Check if submenus exist
            const submenus = document.querySelectorAll('.nav-submenu');
            results.push({
                test: 'Submenus',
                result: submenus.length > 0,
                details: `Found ${submenus.length} submenus`
            });
            
            // Test 4: Check if CSS is loaded
            const submenu = document.querySelector('.nav-submenu');
            const computedStyle = submenu ? window.getComputedStyle(submenu) : null;
            const hasTransition = computedStyle ? computedStyle.transition.includes('all') : false;
            results.push({
                test: 'CSS Transitions',
                result: hasTransition,
                details: hasTransition ? 'CSS transitions loaded' : 'CSS transitions missing'
            });
            
            displayResults(results);
        });
        
        document.getElementById('manual-test').addEventListener('click', function() {
            alert('Now try clicking on the "User Management" and "System Settings" items to test the dropdown functionality manually.');
        });
        
        function displayResults(results) {
            const container = document.getElementById('test-results');
            const passed = results.filter(r => r.result).length;
            const total = results.length;
            
            let html = `<h3 class="font-bold mb-2">Test Results: ${passed}/${total} Passed</h3>`;
            
            results.forEach(result => {
                const icon = result.result ? '✅' : '❌';
                const bgColor = result.result ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                
                html += `
                    <div class="mb-2 p-2 rounded ${bgColor}">
                        ${icon} <strong>${result.test}:</strong> ${result.details}
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }
        
        // Initialize AdminNavigation if it exists
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof AdminNavigation !== 'undefined') {
                window.adminNav = new AdminNavigation();
                console.log('AdminNavigation initialized for testing');
            } else {
                console.error('AdminNavigation class not found');
            }
        });
    </script>
</body>
</html>
