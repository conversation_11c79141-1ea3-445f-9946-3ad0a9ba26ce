{% extends 'base.html' %}
{% load static %}

{% block title %}Create System Notification{% endblock %}
{% block page_title %}Create System Notification{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6 max-w-4xl">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Create System Notification</h1>
            <p class="text-gray-600">Send notifications to users across the system</p>
        </div>
        <a href="{% url 'notification_center' %}" 
           class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Notifications
        </a>
    </div>

    <!-- Create Notification Form -->
    <div class="bg-white rounded-lg shadow">
        <form method="post" class="p-6">
            {% csrf_token %}
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Title -->
                <div class="md:col-span-2">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2">Title</label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter notification title">
                </div>
                
                <!-- Message -->
                <div class="md:col-span-2">
                    <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <textarea id="message" 
                              name="message" 
                              rows="4"
                              required
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              placeholder="Enter notification message"></textarea>
                </div>
                
                <!-- Notification Type -->
                <div>
                    <label for="notification_type" class="block text-sm font-medium text-gray-700 mb-2">Type</label>
                    <select id="notification_type" 
                            name="notification_type" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        {% for value, label in notification_types %}
                            <option value="{{ value }}">{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Priority -->
                <div>
                    <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority</label>
                    <select id="priority" 
                            name="priority" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        {% for value, label in priority_levels %}
                            <option value="{{ value }}" {% if value == 'normal' %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Target Roles -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Target Roles</label>
                    <div class="space-y-2">
                        {% for value, label in user_roles %}
                            <div class="flex items-center">
                                <input type="checkbox" 
                                       id="role_{{ value }}" 
                                       name="target_roles" 
                                       value="{{ value }}"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                                <label for="role_{{ value }}" class="ml-2 text-sm text-gray-700">{{ label }}</label>
                            </div>
                        {% endfor %}
                        <p class="text-xs text-gray-500 mt-1">Leave unchecked to send to all users</p>
                    </div>
                </div>
                
                <!-- Expiration -->
                <div>
                    <label for="expires_in_days" class="block text-sm font-medium text-gray-700 mb-2">Expires In (Days)</label>
                    <input type="number" 
                           id="expires_in_days" 
                           name="expires_in_days" 
                           min="1"
                           max="365"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="7">
                    <p class="text-xs text-gray-500 mt-1">Leave empty for no expiration</p>
                </div>
            </div>
            
            <!-- Submit Button -->
            <div class="mt-6 flex justify-end space-x-3">
                <a href="{% url 'notification_center' %}" 
                   class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                    Cancel
                </a>
                <button type="submit" 
                        class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Create Notification
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
