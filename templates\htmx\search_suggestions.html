{% if items or requests %}
    <div class="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
        {% if items %}
            <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 border-b border-gray-200">
                Supply Items
            </div>
            {% for item in items %}
                <a href="{% url 'supply_item_detail' item.id %}" 
                   class="flex items-center px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ item.name }}</p>
                        <p class="text-xs text-gray-500">{{ item.category.name }} • {{ item.current_stock }} {{ item.unit_of_measure }}</p>
                    </div>
                    <div class="ml-3 flex-shrink-0">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if item.current_stock == 0 %}bg-red-100 text-red-800
                            {% elif item.current_stock <= item.minimum_stock %}bg-yellow-100 text-yellow-800
                            {% else %}bg-green-100 text-green-800{% endif %}">
                            {% if item.current_stock == 0 %}Out
                            {% elif item.current_stock <= item.minimum_stock %}Low
                            {% else %}OK{% endif %}
                        </span>
                    </div>
                </a>
            {% endfor %}
        {% endif %}
        
        {% if requests %}
            <div class="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wide bg-gray-50 border-b border-gray-200">
                Requests
            </div>
            {% for request in requests %}
                <a href="{% url 'request_detail' request.id %}" 
                   class="flex items-center px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">{{ request.request_number }}</p>
                        <p class="text-xs text-gray-500">{{ request.requester.get_full_name|default:request.requester.username }} • {{ request.department }}</p>
                    </div>
                    <div class="ml-3 flex-shrink-0">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if request.status == 'pending' %}bg-yellow-100 text-yellow-800
                            {% elif request.status == 'approved' %}bg-green-100 text-green-800
                            {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                            {% elif request.status == 'released' %}bg-purple-100 text-purple-800
                            {% else %}bg-gray-100 text-gray-800{% endif %}">
                            {{ request.get_status_display }}
                        </span>
                    </div>
                </a>
            {% endfor %}
        {% endif %}
        
        <!-- View all results -->
        <div class="px-3 py-2 bg-gray-50 border-t border-gray-200">
            <a href="{% url 'inventory_list' %}?search={{ query }}" 
               class="text-xs text-blue-600 hover:text-blue-500 font-medium">
                View all results for "{{ query }}" →
            </a>
        </div>
    </div>
{% endif %}
