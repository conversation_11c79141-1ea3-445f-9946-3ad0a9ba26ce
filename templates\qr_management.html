{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900 mb-2">QR Code Management</h1>
            <p class="text-gray-600">Generate and manage QR codes for supply items</p>
        </div>
        <div class="flex flex-col sm:flex-row gap-2 mt-4 sm:mt-0">
            <a href="{% url 'qr_code_list' %}" 
               class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-center">
                <i class="fas fa-list mr-2"></i>Manage QR Codes
            </a>
            <a href="{% url 'qr_scanner' %}" 
               class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center">
                <i class="fas fa-qrcode mr-2"></i>QR Scanner
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <!-- Total Items -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <i class="fas fa-boxes text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Items</p>
                    <p class="text-2xl font-bold text-gray-900">{{ total_items }}</p>
                </div>
            </div>
        </div>

        <!-- Items with QR Codes -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <i class="fas fa-qrcode text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">With QR Codes</p>
                    <p class="text-2xl font-bold text-gray-900">{{ items_with_qr }}</p>
                    <p class="text-xs text-gray-500">
                        {% if total_items > 0 %}
                            {{ items_with_qr|floatformat:0 }}/{{ total_items }} ({% widthratio items_with_qr total_items 100 %}%)
                        {% else %}
                            0%
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>

        <!-- Items without QR Codes -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Without QR Codes</p>
                    <p class="text-2xl font-bold text-gray-900">{{ items_without_qr }}</p>
                    <p class="text-xs text-gray-500">
                        {% if total_items > 0 %}
                            {{ items_without_qr|floatformat:0 }}/{{ total_items }} ({% widthratio items_without_qr total_items 100 %}%)
                        {% else %}
                            0%
                        {% endif %}
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent QR Code Generations -->
    <div class="bg-white rounded-lg shadow-md">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent QR Code Generations</h2>
        </div>
        <div class="p-6">
            {% if recent_qr_items %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {% for item in recent_qr_items %}
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex-1">
                                    <h3 class="font-medium text-gray-900 truncate">{{ item.name }}</h3>
                                    <p class="text-sm text-gray-600">{{ item.category.name }}</p>
                                </div>
                                {% if item.qr_code %}
                                    <div class="ml-2">
                                        <img src="{{ item.qr_code.url }}" alt="QR Code" class="w-12 h-12 object-contain">
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-3">
                                <span>Stock: {{ item.current_stock }} {{ item.unit_of_measure }}</span>
                                <span class="px-2 py-1 rounded-full text-xs {% if item.is_low_stock %}bg-red-100 text-red-800{% elif item.is_out_of_stock %}bg-gray-100 text-gray-800{% else %}bg-green-100 text-green-800{% endif %}">
                                    {% if item.is_out_of_stock %}
                                        Out of Stock
                                    {% elif item.is_low_stock %}
                                        Low Stock
                                    {% else %}
                                        In Stock
                                    {% endif %}
                                </span>
                            </div>
                            
                            <div class="flex gap-2">
                                <button onclick="previewQRCode({{ item.id }})" 
                                        class="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm hover:bg-blue-100 transition-colors">
                                    <i class="fas fa-eye mr-1"></i>Preview
                                </button>
                                <a href="{% url 'download_qr_code' item.id %}" 
                                   class="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded text-sm hover:bg-green-100 transition-colors text-center">
                                    <i class="fas fa-download mr-1"></i>Download
                                </a>
                                <button onclick="regenerateQRCode({{ item.id }})" 
                                        class="flex-1 bg-yellow-50 text-yellow-600 px-3 py-2 rounded text-sm hover:bg-yellow-100 transition-colors">
                                    <i class="fas fa-sync mr-1"></i>Regenerate
                                </button>
                            </div>
                        </div>
                    {% endfor %}
                </div>
                
                <div class="mt-6 text-center">
                    <a href="{% url 'qr_code_list' %}" 
                       class="text-blue-600 hover:text-blue-800 font-medium">
                        View All QR Codes <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
            {% else %}
                <div class="text-center py-8">
                    <i class="fas fa-qrcode text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600 mb-4">No QR codes generated yet</p>
                    <a href="{% url 'qr_code_list' %}" 
                       class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        Generate QR Codes
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- QR Code Preview Modal -->
<div id="qrPreviewModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full" id="qrPreviewContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<script>
function previewQRCode(itemId) {
    fetch(`/qr-codes/preview/${itemId}/`, {
        headers: {
            'HX-Request': 'true'
        }
    })
    .then(response => response.text())
    .then(html => {
        document.getElementById('qrPreviewContent').innerHTML = html;
        document.getElementById('qrPreviewModal').classList.remove('hidden');
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to load QR code preview');
    });
}

function closeQRPreview() {
    document.getElementById('qrPreviewModal').classList.add('hidden');
}

function regenerateQRCode(itemId) {
    if (!confirm('Are you sure you want to regenerate the QR code? This will create a new QR code and invalidate the old one.')) {
        return;
    }
    
    fetch(`/qr-codes/regenerate/${itemId}/`, {
        method: 'POST',
        headers: {
            'HX-Request': 'true',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload(); // Refresh to show updated QR code
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to regenerate QR code');
    });
}

// Close modal when clicking outside
document.getElementById('qrPreviewModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQRPreview();
    }
});
</script>
{% endblock %}