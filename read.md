Smart Supply Management System with QR Code Tracking
Build me a modern, mobile-first Smart Supply Management System for JHCSC Dumingag Campus using the following tech stack:

Backend: Django (Python)

Frontend Styling: Tailwind CSS (CDN)

Interactivity: HTMX + Unpoly.js + Alpine.js (CDN-based)

Database: PostgreSQL or SQLite (whichever is easier for dev)

QR Tracking: Use any Django-compatible library to generate and scan QR codes

Mobile First Design: Should be fully mobile-responsive with interactive UI/UX

🎯 Key Features to Implement
Role-Based Access Control (RBAC):

Admin: Full access to inventory, users, approvals, and reports.

GSO Staff: Can approve/reject requests, release supplies, scan/manage QR codes.

Department Users: Can create supply requests and view status/history.

Authentication & Authorization:

Login & Logout with role redirection.

Only authenticated users should access protected routes.

Responsive Collapsible Sidebar Dashboard:

Mobile-first sidebar navigation that toggles open/close.

Should auto-collapse on small screens and expand on desktop.

Use Alpine.js for toggling behavior.

Modules:

Supply Request Module:

Department Users request items.

Display status (Pending, Approved, Released, Rejected).

Approval Module:

GSO can approve/reject requests.

Notifications or status updates.

Inventory Management Module:

Add/edit/delete supplies with stock level display.

View usage history and current stock.

QR Code Tracking Module:

Generate QR code per item on creation.

Scan during issuance and returns using mobile camera.

Track scan logs (who, when, where).

Reports & Logs:

Auto-generated reports: request summaries, inventory levels, usage logs.

HTMX + Unpoly UX Behaviors:

Use HTMX for inline updates, modals, and partial reloads (e.g., approval buttons, item status updates).

Use Unpoly for faster page transitions and modal handling.

Implement inline forms using hx-post with live feedback.

📱 UI/UX and Design Notes
Prioritize mobile users. Must be usable on smartphones and tablets.

Use Tailwind CSS to keep it clean, minimalist, and elegant.

Icons and visual feedback (success/fail/error) should be clearly visible.

Dashboard should include:

Supply stock status (low/high/out).

Quick stats (total requests today, pending, approved, etc.).

Role-based widgets.

⚙️ Technical Constraints & Notes
All QR code scans should log to the database with metadata.

Use existing smartphone/tablet cameras (no extra hardware).

All forms and tables should work without full-page reloads.

Include search and filters for inventory, requests, and QR logs.

