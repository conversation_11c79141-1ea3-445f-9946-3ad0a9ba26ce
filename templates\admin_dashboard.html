{% extends 'base.html' %}
{% load static %}

{% block title %}Admin Dashboard - Smart Supply Management{% endblock %}

{% block page_title %}Admin Dashboard{% endblock %}

{% block extra_css %}
<!-- Admin-specific CSS is now loaded in base.html -->
{% endblock %}

{% block content %}
<!-- Dashboard Statistics with HTMX auto-refresh -->
<div id="dashboard-stats" 
     hx-get="{% url 'dashboard_stats_htmx' %}" 
     hx-trigger="load, every 30s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_stats.html' %}
</div>

<!-- Admin Quick Actions with HTMX -->
<div id="dashboard-widgets" 
     hx-get="{% url 'dashboard_widgets_htmx' %}" 
     hx-trigger="load, every 60s"
     hx-swap="innerHTML"
     class="mb-8">
    {% include 'partials/dashboard_widgets.html' %}
</div>

<!-- System Activity with HTMX auto-refresh -->
<div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900">System Activity</h3>
            <button hx-get="{% url 'dashboard_activities_htmx' %}" 
                    hx-target="#dashboard-activities"
                    hx-swap="innerHTML"
                    class="text-sm text-blue-600 hover:text-blue-500 flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Refresh
            </button>
        </div>
        <div id="dashboard-activities" 
             hx-get="{% url 'dashboard_activities_htmx' %}" 
             hx-trigger="load, every 45s"
             hx-swap="innerHTML">
            {% include 'partials/dashboard_activities.html' %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script src="{% static 'js/admin_navigation.js' %}"></script>
<script>
    // Admin Dashboard-specific JavaScript
    document.addEventListener('DOMContentLoaded', function() {
        // Add loading states for HTMX requests
        document.body.addEventListener('htmx:beforeRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities' || target.id === 'admin-quick-stats') {
                target.style.opacity = '0.7';
            }
        });

        document.body.addEventListener('htmx:afterRequest', function(evt) {
            const target = evt.target;
            if (target.id === 'dashboard-stats' || target.id === 'dashboard-widgets' || target.id === 'dashboard-activities' || target.id === 'admin-quick-stats') {
                target.style.opacity = '1';
            }
        });

        // Initialize admin-specific features
        initializeAdminDashboard();
    });

    function initializeAdminDashboard() {
        // Add keyboard shortcuts help
        document.addEventListener('keydown', function(e) {
            if (e.key === '?' && e.shiftKey) {
                showKeyboardShortcutsModal();
            }
        });

        // Initialize tooltips for admin features
        initializeAdminTooltips();
    }

    function showKeyboardShortcutsModal() {
        // Create and show keyboard shortcuts modal
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold mb-4">Admin Keyboard Shortcuts</h3>
                <div class="space-y-2 text-sm">
                    <div class="flex justify-between"><span>Dashboard</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + D</kbd></div>
                    <div class="flex justify-between"><span>Users</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + U</kbd></div>
                    <div class="flex justify-between"><span>Settings</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + S</kbd></div>
                    <div class="flex justify-between"><span>Inventory</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + I</kbd></div>
                    <div class="flex justify-between"><span>Requests</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + R</kbd></div>
                    <div class="flex justify-between"><span>Reports</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + A</kbd></div>
                    <div class="flex justify-between"><span>Audit Logs</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + L</kbd></div>
                    <div class="flex justify-between"><span>Notifications</span><kbd class="bg-gray-100 px-2 py-1 rounded">Alt + N</kbd></div>
                </div>
                <button onclick="this.closest('.fixed').remove()" class="mt-4 w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700">Close</button>
            </div>
        `;
        document.body.appendChild(modal);

        // Close on click outside
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }

    function initializeAdminTooltips() {
        // Add tooltips to navigation items
        const navItems = document.querySelectorAll('[data-nav-item]');
        navItems.forEach(item => {
            const navItemName = item.getAttribute('data-nav-item');
            const shortcut = getShortcutForNavItem(navItemName);
            if (shortcut) {
                item.title = `${item.textContent.trim()} (${shortcut})`;
            }
        });
    }

    function getShortcutForNavItem(navItem) {
        const shortcuts = {
            'dashboard': 'Alt + D',
            'users': 'Alt + U',
            'settings': 'Alt + S',
            'inventory': 'Alt + I',
            'requests': 'Alt + R',
            'reports': 'Alt + A',
            'audit-logs': 'Alt + L',
            'notifications': 'Alt + N'
        };
        return shortcuts[navItem];
    }
</script>
{% endblock %}