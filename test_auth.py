#!/usr/bin/env python
"""
Simple test script to verify authentication system functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from suptrack.models import UserProfile

def test_authentication_system():
    """Test the authentication system components"""
    print("Testing Smart Supply Authentication System")
    print("=" * 50)
    
    # Test 1: Check if test users exist
    print("\n1. Checking test users...")
    try:
        admin_user = User.objects.get(username='admin')
        gso_user = User.objects.get(username='gso_staff')
        dept_user = User.objects.get(username='dept_user')
        print("✓ All test users exist")
    except User.DoesNotExist as e:
        print(f"✗ Missing user: {e}")
        return False
    
    # Test 2: Check if user profiles exist
    print("\n2. Checking user profiles...")
    try:
        admin_profile = admin_user.userprofile
        gso_profile = gso_user.userprofile
        dept_profile = dept_user.userprofile
        
        print(f"✓ Admin profile: {admin_profile.role}")
        print(f"✓ GSO profile: {gso_profile.role}")
        print(f"✓ Department profile: {dept_profile.role}")
    except UserProfile.DoesNotExist as e:
        print(f"✗ Missing profile: {e}")
        return False
    
    # Test 3: Test login functionality
    print("\n3. Testing login functionality...")
    from django.test import Client
    client = Client()
    
    # Test admin login using Django's login method
    login_success = client.login(username='admin', password='admin123')
    
    if login_success:
        print("✓ Admin login successful")
    else:
        print("✗ Admin login failed")
        return False
    
    # Test role-based redirection
    print("\n4. Testing role-based dashboard access...")
    
    # Test admin dashboard access
    response = client.get('/admin-dashboard/')
    if response.status_code == 200:
        print("✓ Admin dashboard accessible")
    else:
        print(f"✗ Admin dashboard access failed: {response.status_code}")
    
    # Logout
    client.logout()
    
    # Test GSO staff login and dashboard
    client.logout()
    login_success = client.login(username='gso_staff', password='gso123')
    
    if login_success:
        print("✓ GSO staff login successful")
        
        response = client.get('/gso-dashboard/')
        if response.status_code == 200:
            print("✓ GSO dashboard accessible")
        else:
            print(f"✗ GSO dashboard access failed: {response.status_code}")
    else:
        print("✗ GSO staff login failed")
    
    # Test department user
    client.logout()
    login_success = client.login(username='dept_user', password='dept123')
    
    if login_success:
        print("✓ Department user login successful")
        
        response = client.get('/dashboard/')
        if response.status_code == 200:
            print("✓ Department dashboard accessible")
        else:
            print(f"✗ Department dashboard access failed: {response.status_code}")
    else:
        print("✗ Department user login failed")
    
    print("\n" + "=" * 50)
    print("Authentication system test completed!")
    return True

if __name__ == '__main__':
    test_authentication_system()