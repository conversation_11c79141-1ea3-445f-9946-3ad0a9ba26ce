"""
Django signals for automatic notification creation
"""
from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.contrib.auth.models import User
from .models import SupplyItem, SupplyRequest, Notification


@receiver(pre_save, sender=SupplyItem)
def capture_old_stock_level(sender, instance, **kwargs):
    """Capture the old stock level before saving"""
    if instance.pk:
        try:
            old_instance = SupplyItem.objects.get(pk=instance.pk)
            instance._old_stock = old_instance.current_stock
        except SupplyItem.DoesNotExist:
            instance._old_stock = None
    else:
        instance._old_stock = None


@receiver(post_save, sender=SupplyItem)
def create_stock_notifications(sender, instance, created, **kwargs):
    """Create notifications when stock levels change"""
    if created:
        return  # Don't create notifications for new items
    
    old_stock = getattr(instance, '_old_stock', None)
    if old_stock is None or old_stock == instance.current_stock:
        return  # No change in stock level
    
    # Check if item went from having stock to being out of stock
    if old_stock > 0 and instance.current_stock == 0:
        Notification.create_out_of_stock_alert(instance)
    
    # Check if item went from normal stock to low stock
    elif old_stock > instance.minimum_stock and instance.current_stock <= instance.minimum_stock and instance.current_stock > 0:
        Notification.create_low_stock_alert(instance)
    
    # Check if item was restocked from out of stock
    elif old_stock == 0 and instance.current_stock > 0:
        # Create a positive notification for restocking
        admin_users = User.objects.filter(userprofile__role__in=['admin', 'gso_staff'])
        
        for user in admin_users:
            Notification.create_notification(
                recipient=user,
                notification_type='system_alert',
                title=f'Item Restocked: {instance.name}',
                message=f'{instance.name} has been restocked. Current stock: {instance.current_stock} {instance.unit_of_measure}',
                priority='normal',
                supply_item=instance,
                action_url=f'/inventory/{instance.id}/',
                action_text='View Item',
                expires_in_days=7
            )


@receiver(pre_save, sender=SupplyRequest)
def capture_old_request_status(sender, instance, **kwargs):
    """Capture the old request status before saving"""
    if instance.pk:
        try:
            old_instance = SupplyRequest.objects.get(pk=instance.pk)
            instance._old_status = old_instance.status
        except SupplyRequest.DoesNotExist:
            instance._old_status = None
    else:
        instance._old_status = None


@receiver(post_save, sender=SupplyRequest)
def create_request_status_notifications(sender, instance, created, **kwargs):
    """Create notifications when request status changes"""
    if created:
        # For new requests, notify admins and GSO staff
        if instance.status == 'pending':
            admin_users = User.objects.filter(userprofile__role__in=['admin', 'gso_staff'])
            
            for user in admin_users:
                Notification.create_notification(
                    recipient=user,
                    notification_type='request_pending',
                    title=f'New Request Pending: {instance.request_number}',
                    message=f'A new supply request from {instance.requester.get_full_name() or instance.requester.username} ({instance.department}) is pending approval.',
                    priority='normal',
                    supply_request=instance,
                    action_url=f'/approvals/',
                    action_text='Review Request',
                    expires_in_days=30
                )
        return
    
    old_status = getattr(instance, '_old_status', None)
    if old_status is None or old_status == instance.status:
        return  # No change in status
    
    # Create notification for status change
    Notification.create_request_status_notification(instance, old_status, instance.status)
