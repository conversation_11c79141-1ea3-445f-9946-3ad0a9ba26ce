"""
Advanced HTMX views for enhanced user experience
"""
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.template.loader import render_to_string
from django.contrib import messages
from django.core.exceptions import ValidationError
from django.db import transaction
from django.db import models
from django.db.models import F
import json

from .models import (
    SupplyRequest, RequestItem, SupplyItem, SupplyCategory,
    InventoryTransaction, QRScanLog, UserProfile
)
from .decorators import role_required
from .forms import SupplyItemForm, RequestItemForm


@login_required
@role_required(['admin', 'gso_staff'])
def inline_edit_supply_item(request, item_id):
    """HTMX endpoint for inline editing supply items"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    if request.method == 'GET':
        # Return edit form
        form = SupplyItemForm(instance=supply_item)
        return render(request, 'htmx/inline_edit_supply_item.html', {
            'supply_item': supply_item,
            'form': form
        })
    
    elif request.method == 'POST':
        # Process form submission
        form = SupplyItemForm(request.POST, instance=supply_item)
        
        if form.is_valid():
            try:
                with transaction.atomic():
                    updated_item = form.save()
                    
                    # Log the update
                    if 'current_stock' in form.changed_data:
                        InventoryTransaction.objects.create(
                            supply_item=updated_item,
                            transaction_type='adjustment',
                            quantity=abs(updated_item.current_stock - supply_item.current_stock),
                            previous_stock=supply_item.current_stock,
                            new_stock=updated_item.current_stock,
                            performed_by=request.user,
                            reference_number=f'INLINE-EDIT-{updated_item.id}',
                            notes='Inline edit adjustment'
                        )
                
                # Return updated display view
                return render(request, 'htmx/supply_item_display.html', {
                    'supply_item': updated_item,
                    'success_message': 'Item updated successfully!'
                })
                
            except Exception as e:
                form.add_error(None, f'Error updating item: {str(e)}')
        
        # Return form with errors
        return render(request, 'htmx/inline_edit_supply_item.html', {
            'supply_item': supply_item,
            'form': form
        })


@login_required
def inline_edit_request_item(request, item_id):
    """HTMX endpoint for inline editing request items"""
    request_item = get_object_or_404(RequestItem, id=item_id)
    
    # Check permissions
    if not (request.user == request_item.request.requester or 
            request.user.userprofile.role in ['admin', 'gso_staff']):
        return HttpResponse('Unauthorized', status=403)
    
    # Only allow editing if request is pending
    if request_item.request.status != 'pending':
        return HttpResponse('Cannot edit approved/rejected requests', status=400)
    
    if request.method == 'GET':
        # Return edit form
        form = RequestItemForm(instance=request_item)
        return render(request, 'htmx/inline_edit_request_item.html', {
            'request_item': request_item,
            'form': form
        })
    
    elif request.method == 'POST':
        # Process form submission
        form = RequestItemForm(request.POST, instance=request_item)
        
        if form.is_valid():
            try:
                updated_item = form.save()
                
                # Return updated display view
                return render(request, 'htmx/request_item_display.html', {
                    'request_item': updated_item,
                    'success_message': 'Request item updated successfully!'
                })
                
            except Exception as e:
                form.add_error(None, f'Error updating request item: {str(e)}')
        
        # Return form with errors
        return render(request, 'htmx/inline_edit_request_item.html', {
            'request_item': request_item,
            'form': form
        })


@login_required
@role_required(['admin', 'gso_staff'])
def quick_stock_adjustment(request, item_id):
    """HTMX endpoint for quick stock adjustments"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    
    if request.method == 'GET':
        return render(request, 'htmx/quick_stock_adjustment.html', {
            'supply_item': supply_item
        })
    
    elif request.method == 'POST':
        try:
            adjustment_type = request.POST.get('adjustment_type')  # 'add', 'subtract', 'set'
            quantity = int(request.POST.get('quantity', 0))
            notes = request.POST.get('notes', '')
            
            if quantity <= 0:
                raise ValidationError('Quantity must be positive')
            
            previous_stock = supply_item.current_stock
            
            if adjustment_type == 'add':
                new_stock = previous_stock + quantity
                transaction_quantity = quantity
                transaction_type = 'in'
            elif adjustment_type == 'subtract':
                new_stock = max(0, previous_stock - quantity)
                transaction_quantity = min(quantity, previous_stock)
                transaction_type = 'out'
            elif adjustment_type == 'set':
                new_stock = quantity
                transaction_quantity = abs(new_stock - previous_stock)
                transaction_type = 'adjustment'
            else:
                raise ValidationError('Invalid adjustment type')
            
            with transaction.atomic():
                # Update stock
                supply_item.current_stock = new_stock
                supply_item.save()
                
                # Log transaction
                InventoryTransaction.objects.create(
                    supply_item=supply_item,
                    transaction_type=transaction_type,
                    quantity=transaction_quantity,
                    previous_stock=previous_stock,
                    new_stock=new_stock,
                    performed_by=request.user,
                    reference_number=f'QUICK-ADJ-{supply_item.id}',
                    notes=notes or f'Quick {adjustment_type} adjustment'
                )
            
            # Return updated display
            return render(request, 'htmx/supply_item_display.html', {
                'supply_item': supply_item,
                'success_message': f'Stock adjusted from {previous_stock} to {new_stock}'
            })
            
        except (ValueError, ValidationError) as e:
            return render(request, 'htmx/quick_stock_adjustment.html', {
                'supply_item': supply_item,
                'error_message': str(e)
            })


@login_required
def live_notifications(request):
    """HTMX endpoint for live notifications"""
    user_profile = request.user.userprofile
    
    notifications = []
    
    # Low stock notifications for GSO staff and admins
    if user_profile.role in ['admin', 'gso_staff']:
        low_stock_items = SupplyItem.objects.filter(
            current_stock__lte=F('minimum_stock'),
            current_stock__gt=0
        ).count()

        out_of_stock_items = SupplyItem.objects.filter(current_stock=0).count()
        
        if low_stock_items > 0:
            notifications.append({
                'type': 'warning',
                'message': f'{low_stock_items} item(s) are running low on stock',
                'url': '/inventory/low-stock/',
                'icon': 'exclamation-triangle'
            })
        
        if out_of_stock_items > 0:
            notifications.append({
                'type': 'error',
                'message': f'{out_of_stock_items} item(s) are out of stock',
                'url': '/inventory/?stock_level=out',
                'icon': 'x-circle'
            })
    
    # Pending requests for admins and GSO staff
    if user_profile.role in ['admin', 'gso_staff']:
        pending_count = SupplyRequest.objects.filter(status='pending').count()
        if pending_count > 0:
            notifications.append({
                'type': 'info',
                'message': f'{pending_count} request(s) pending approval',
                'url': '/approvals/',
                'icon': 'clock'
            })
    
    # User's own requests status updates
    user_requests = SupplyRequest.objects.filter(
        requester=request.user,
        status__in=['approved', 'rejected']
    ).order_by('-approved_date')[:3]
    
    for req in user_requests:
        if req.status == 'approved':
            notifications.append({
                'type': 'success',
                'message': f'Request {req.request_number} was approved',
                'url': f'/requests/{req.id}/',
                'icon': 'check-circle'
            })
        elif req.status == 'rejected':
            notifications.append({
                'type': 'error',
                'message': f'Request {req.request_number} was rejected',
                'url': f'/requests/{req.id}/',
                'icon': 'x-circle'
            })
    
    return render(request, 'htmx/live_notifications.html', {
        'notifications': notifications
    })


@login_required
def status_indicator(request, model_type, object_id):
    """HTMX endpoint for live status indicators"""
    try:
        if model_type == 'request':
            obj = get_object_or_404(SupplyRequest, id=object_id)
            status = obj.status
            status_display = obj.get_status_display()
            
        elif model_type == 'item':
            obj = get_object_or_404(SupplyItem, id=object_id)
            if obj.current_stock == 0:
                status = 'out_of_stock'
                status_display = 'Out of Stock'
            elif obj.current_stock <= obj.minimum_stock:
                status = 'low_stock'
                status_display = 'Low Stock'
            else:
                status = 'normal'
                status_display = 'Normal'
        else:
            return HttpResponse('Invalid model type', status=400)
        
        return render(request, 'htmx/status_indicator.html', {
            'status': status,
            'status_display': status_display,
            'model_type': model_type,
            'object_id': object_id
        })
        
    except Exception as e:
        return HttpResponse(f'Error: {str(e)}', status=500)


@login_required
@require_http_methods(["POST"])
def toggle_favorite_item(request, item_id):
    """HTMX endpoint to toggle favorite items"""
    supply_item = get_object_or_404(SupplyItem, id=item_id)
    user_profile = request.user.userprofile
    
    # Toggle favorite status (this would require a ManyToMany field in UserProfile)
    # For now, we'll simulate this with a simple response
    
    is_favorite = request.POST.get('is_favorite') == 'true'
    
    return render(request, 'htmx/favorite_button.html', {
        'supply_item': supply_item,
        'is_favorite': not is_favorite  # Toggle the state
    })


@login_required
def search_suggestions(request):
    """HTMX endpoint for search suggestions"""
    query = request.GET.get('q', '').strip()
    
    if len(query) < 2:
        return HttpResponse('')
    
    # Search supply items
    items = SupplyItem.objects.filter(
        name__icontains=query
    ).select_related('category')[:5]
    
    # Search requests (if user has permission)
    requests = []
    if request.user.userprofile.role in ['admin', 'gso_staff']:
        requests = SupplyRequest.objects.filter(
            request_number__icontains=query
        ).select_related('requester')[:3]
    
    return render(request, 'htmx/search_suggestions.html', {
        'query': query,
        'items': items,
        'requests': requests
    })
