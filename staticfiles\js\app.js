// Smart Supply Management System - Main JavaScript

// QR Code Scanner functionality
class QRScanner {
    constructor() {
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.scanning = false;
    }

    async startScanning(videoElement, onScanSuccess, onScanError) {
        this.video = videoElement;
        this.canvas = document.createElement('canvas');
        this.context = this.canvas.getContext('2d');

        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: 'environment' }
            });
            
            this.video.srcObject = stream;
            this.video.play();
            this.scanning = true;

            this.video.addEventListener('loadedmetadata', () => {
                this.canvas.width = this.video.videoWidth;
                this.canvas.height = this.video.videoHeight;
                this.scanFrame(onScanSuccess, onScanError);
            });

        } catch (error) {
            console.error('Error accessing camera:', error);
            if (onScanError) onScanError('Camera access denied or not available');
        }
    }

    scanFrame(onScanSuccess, onScanError) {
        if (!this.scanning) return;

        if (this.video.readyState === this.video.HAVE_ENOUGH_DATA) {
            this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
            const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
            
            if (typeof jsQR !== 'undefined') {
                const code = jsQR(imageData.data, imageData.width, imageData.height);
                
                if (code) {
                    this.stopScanning();
                    if (onScanSuccess) onScanSuccess(code.data);
                    return;
                }
            }
        }

        requestAnimationFrame(() => this.scanFrame(onScanSuccess, onScanError));
    }

    stopScanning() {
        this.scanning = false;
        if (this.video && this.video.srcObject) {
            const tracks = this.video.srcObject.getTracks();
            tracks.forEach(track => track.stop());
            this.video.srcObject = null;
        }
    }
}

// Global QR Scanner instance
window.qrScanner = new QRScanner();

// HTMX Event Handlers
document.addEventListener('DOMContentLoaded', function() {
    // Handle HTMX form submissions
    document.body.addEventListener('htmx:beforeRequest', function(evt) {
        // Show loading state
        const target = evt.target;
        if (target.tagName === 'FORM') {
            const submitBtn = target.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg>Processing...';
            }
        }
    });

    document.body.addEventListener('htmx:afterRequest', function(evt) {
        // Reset form state
        const target = evt.target;
        if (target.tagName === 'FORM') {
            const submitBtn = target.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = submitBtn.getAttribute('data-original-text') || 'Submit';
            }
        }
    });

    // Store original button text
    document.querySelectorAll('button[type="submit"]').forEach(btn => {
        btn.setAttribute('data-original-text', btn.innerHTML);
    });
});

// Utility functions
window.SmartSupply = {
    // Show notification
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'warning' ? 'bg-yellow-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    },

    // Format currency
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(amount);
    },

    // Format date
    formatDate: function(dateString) {
        return new Date(dateString).toLocaleDateString('en-PH', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    },

    // Confirm action
    confirmAction: function(message, callback) {
        if (confirm(message)) {
            callback();
        }
    }
};

// Alpine.js global data
document.addEventListener('alpine:init', () => {
    Alpine.data('qrScanner', () => ({
        scanning: false,
        result: '',
        error: '',

        startScan() {
            const video = this.$refs.video;
            this.scanning = true;
            this.error = '';

            window.qrScanner.startScanning(
                video,
                (data) => {
                    this.result = data;
                    this.scanning = false;
                    this.$dispatch('qr-scanned', { data });
                },
                (error) => {
                    this.error = error;
                    this.scanning = false;
                }
            );
        },

        stopScan() {
            window.qrScanner.stopScanning();
            this.scanning = false;
        }
    }));

    Alpine.data('sidebar', () => ({
        open: false,
        
        toggle() {
            this.open = !this.open;
        },

        close() {
            this.open = false;
        }
    }));
});