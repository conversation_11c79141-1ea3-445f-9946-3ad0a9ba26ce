# Design Document

## Overview

The Smart Supply Management System is a Django-based web application that provides comprehensive supply chain management for JHCSC Dumingag Campus. The system leverages modern web technologies including HTMX, Unpoly.js, and Alpine.js for interactive user experiences, while maintaining a mobile-first responsive design using Tailwind CSS.

The architecture follows Django's MVT (Model-View-Template) pattern with role-based access control, QR code integration, and real-time updates without full page reloads.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[Mobile/Desktop Browser] --> B[Django Web Server]
    B --> C[Authentication Middleware]
    C --> D[Role-Based Views]
    D --> E[Business Logic Layer]
    E --> F[Django ORM]
    F --> G[SQLite Database]
    
    H[QR Code Scanner] --> I[Camera API]
    I --> J[QR Processing]
    J --> E
    
    K[Static Assets] --> L[Tailwind CSS]
    K --> M[HTMX/Unpoly/Alpine.js]
    
    subgraph "Frontend Technologies"
        L
        M
        N[Responsive Components]
    end
```

### Technology Stack

- **Backend Framework**: Django 5.2.4
- **Database**: SQLite (existing setup)
- **Frontend Styling**: Tailwind CSS (CDN)
- **Interactive Behavior**: HTMX + Unpoly.js + Alpine.js (CDN)
- **QR Code Generation**: python-qrcode + Pillow
- **QR Code Scanning**: HTML5 Camera API + jsQR library
- **Authentication**: Django's built-in authentication system
- **File Storage**: Django's default file storage for QR code images

## Components and Interfaces

### 1. Authentication and Authorization Component

**Purpose**: Manage user authentication and role-based access control

**Key Classes**:
- `UserProfile` (extends Django User model)
- `RolePermissionMixin`
- `AuthenticationViews`

**Interfaces**:
- Login/Logout endpoints
- Role-based view decorators
- Permission checking utilities

### 2. Dashboard Component

**Purpose**: Provide role-specific dashboards with real-time statistics

**Key Classes**:
- `DashboardView`
- `StatsAggregator`
- `WidgetRenderer`

**Interfaces**:
- Dashboard data API endpoints
- Widget configuration system
- Real-time update mechanisms via HTMX

### 3. Supply Request Management Component

**Purpose**: Handle supply request lifecycle from creation to fulfillment

**Key Classes**:
- `SupplyRequest`
- `RequestItem`
- `RequestWorkflow`

**Interfaces**:
- Request CRUD operations
- Status update endpoints
- Request history API

### 4. Approval Workflow Component

**Purpose**: Manage request approval process for GSO staff

**Key Classes**:
- `ApprovalWorkflow`
- `ApprovalAction`
- `NotificationService`

**Interfaces**:
- Approval/rejection endpoints
- Bulk action processing
- Status notification system

### 5. Inventory Management Component

**Purpose**: Track supply items, stock levels, and inventory operations

**Key Classes**:
- `SupplyItem`
- `StockLevel`
- `InventoryTransaction`

**Interfaces**:
- Inventory CRUD operations
- Stock level monitoring
- Search and filtering API

### 6. QR Code System Component

**Purpose**: Generate, scan, and track QR codes for supply items

**Key Classes**:
- `QRCodeGenerator`
- `QRScanLog`
- `TrackingService`

**Interfaces**:
- QR code generation API
- Scan logging endpoints
- Camera integration interface

### 7. Reporting Component

**Purpose**: Generate reports and analytics for supply management

**Key Classes**:
- `ReportGenerator`
- `AnalyticsService`
- `ExportService`

**Interfaces**:
- Report generation endpoints
- Data export API
- Analytics dashboard API

## Data Models

### User Management Models

```python
class UserProfile(models.Model):
    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('gso_staff', 'GSO Staff'),
        ('department_user', 'Department User'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=15, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

### Supply Management Models

```python
class SupplyCategory(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

class SupplyItem(models.Model):
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(SupplyCategory, on_delete=models.CASCADE)
    unit_of_measure = models.CharField(max_length=50)
    current_stock = models.IntegerField(default=0)
    minimum_stock = models.IntegerField(default=10)
    qr_code = models.ImageField(upload_to='qr_codes/', blank=True)
    qr_code_data = models.CharField(max_length=255, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class SupplyRequest(models.Model):
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('released', 'Released'),
        ('rejected', 'Rejected'),
    ]
    
    request_number = models.CharField(max_length=20, unique=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE)
    department = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    request_date = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_requests')
    approved_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)

class RequestItem(models.Model):
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='items')
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity_requested = models.IntegerField()
    quantity_approved = models.IntegerField(default=0)
    quantity_released = models.IntegerField(default=0)
```

### QR Code Tracking Models

```python
class QRScanLog(models.Model):
    SCAN_TYPE_CHOICES = [
        ('issuance', 'Issuance'),
        ('return', 'Return'),
        ('inventory_check', 'Inventory Check'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    scanned_by = models.ForeignKey(User, on_delete=models.CASCADE)
    scan_type = models.CharField(max_length=20, choices=SCAN_TYPE_CHOICES)
    scan_datetime = models.DateTimeField(auto_now_add=True)
    location = models.CharField(max_length=200, blank=True)
    notes = models.TextField(blank=True)
    request_item = models.ForeignKey(RequestItem, on_delete=models.SET_NULL, null=True, blank=True)

class InventoryTransaction(models.Model):
    TRANSACTION_TYPE_CHOICES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Adjustment'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    quantity = models.IntegerField()
    previous_stock = models.IntegerField()
    new_stock = models.IntegerField()
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_date = models.DateTimeField(auto_now_add=True)
    reference_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)
```

## Error Handling

### Client-Side Error Handling

1. **HTMX Error Responses**: Use HTMX's error handling to display inline error messages
2. **Form Validation**: Real-time validation with Alpine.js for immediate feedback
3. **QR Scanner Errors**: Graceful handling of camera access and scanning failures
4. **Network Errors**: Offline detection and retry mechanisms

### Server-Side Error Handling

1. **Django Exception Handling**: Custom exception classes for business logic errors
2. **Database Constraints**: Proper constraint handling with user-friendly messages
3. **File Upload Errors**: QR code generation and image upload error handling
4. **Permission Errors**: Clear messaging for unauthorized access attempts

### Error Response Format

```python
# Standard error response format for HTMX requests
{
    "success": false,
    "message": "User-friendly error message",
    "errors": {
        "field_name": ["Field-specific error messages"]
    },
    "error_code": "SPECIFIC_ERROR_CODE"
}
```

## Testing Strategy

### Unit Testing

1. **Model Tests**: Test all model methods, validations, and relationships
2. **View Tests**: Test view logic, permissions, and response formats
3. **Utility Tests**: Test QR code generation, scanning utilities, and helper functions
4. **Form Tests**: Test form validation and processing logic

### Integration Testing

1. **Workflow Tests**: Test complete request-to-fulfillment workflows
2. **Authentication Tests**: Test role-based access control across views
3. **API Tests**: Test HTMX endpoints and JSON responses
4. **File Handling Tests**: Test QR code generation and image processing

### Frontend Testing

1. **HTMX Behavior Tests**: Test partial page updates and form submissions
2. **Alpine.js Component Tests**: Test interactive components and state management
3. **Responsive Design Tests**: Test mobile-first design across devices
4. **QR Scanner Tests**: Test camera integration and scanning functionality

### Performance Testing

1. **Database Query Optimization**: Test and optimize N+1 queries
2. **Image Processing Performance**: Test QR code generation speed
3. **Mobile Performance**: Test loading times on mobile devices
4. **Concurrent User Testing**: Test system behavior under load

### Test Data Management

1. **Fixtures**: Create comprehensive test fixtures for all models
2. **Factory Classes**: Use factory_boy for generating test data
3. **Test Database**: Separate test database configuration
4. **Mock Services**: Mock external services and camera API for testing

### Continuous Integration

1. **Automated Test Runs**: Run tests on every commit
2. **Code Coverage**: Maintain minimum 80% code coverage
3. **Linting and Formatting**: Enforce code quality standards
4. **Security Testing**: Regular security vulnerability scans