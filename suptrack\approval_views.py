from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.template.loader import render_to_string
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_protect
from django.db import transaction
from django.utils import timezone
from django.core.paginator import Paginator
from django.db.models import Q, Count, Prefetch
from .models import SupplyRequest, RequestItem, UserProfile, InventoryTransaction
from .decorators import gso_staff_required, admin_or_gso_required
import json


@login_required
@admin_or_gso_required
def pending_requests_view(request):
    """View for GSO staff to see all pending requests with approval actions"""
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    # Base queryset with optimized queries
    requests = SupplyRequest.objects.filter(status='pending').select_related(
        'requester', 'requester__userprofile'
    ).prefetch_related(
        Prefetch('items', queryset=RequestItem.objects.select_related('supply_item'))
    ).order_by('-request_date')
    
    # Apply filters
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(requester__username__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query) |
            Q(department__icontains=search_query)
        )
    
    if department_filter:
        requests = requests.filter(department__icontains=department_filter)
    
    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    
    # Get unique departments for filter dropdown
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'department_filter': department_filter,
        'date_from': date_from,
        'date_to': date_to,
        'departments': departments,
        'total_pending': requests.count()
    }
    
    return render(request, 'approvals/pending_requests.html', context)


@login_required
@admin_or_gso_required
@require_http_methods(["POST"])
@csrf_protect
def approve_request_htmx(request, request_id):
    """HTMX endpoint to approve a single request"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id, status='pending')
    
    try:
        with transaction.atomic():
            # Update request status
            supply_request.status = 'approved'
            supply_request.approved_by = request.user
            supply_request.approved_date = timezone.now()
            supply_request.save()
            
            # Auto-approve all items with requested quantities
            for item in supply_request.items.all():
                item.quantity_approved = item.quantity_requested
                item.save()
            
            # Log the approval action
            log_approval_action(request.user, supply_request, 'approved')
            
            # Return updated row HTML for HTMX
            html = render_to_string('approvals/partials/request_row.html', {
                'request': supply_request,
                'show_actions': False  # Hide actions since it's approved
            })
            
            return HttpResponse(html)
            
    except Exception as e:
        return HttpResponse(
            f'<div class="text-red-600">Error approving request: {str(e)}</div>',
            status=400
        )


@login_required
@admin_or_gso_required
@require_http_methods(["POST"])
@csrf_protect
def reject_request_htmx(request, request_id):
    """HTMX endpoint to reject a request with reason"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id, status='pending')
    
    # Get rejection reason from POST data
    rejection_reason = request.POST.get('rejection_reason', '').strip()
    
    if not rejection_reason:
        return HttpResponse(
            '<div class="text-red-600">Rejection reason is required</div>',
            status=400
        )
    
    try:
        with transaction.atomic():
            # Update request status
            supply_request.status = 'rejected'
            supply_request.approved_by = request.user
            supply_request.approved_date = timezone.now()
            supply_request.rejection_reason = rejection_reason
            supply_request.save()
            
            # Log the rejection action
            log_approval_action(request.user, supply_request, 'rejected', rejection_reason)
            
            # Return updated row HTML for HTMX
            html = render_to_string('approvals/partials/request_row.html', {
                'request': supply_request,
                'show_actions': False  # Hide actions since it's rejected
            })
            
            return HttpResponse(html)
            
    except Exception as e:
        return HttpResponse(
            f'<div class="text-red-600">Error rejecting request: {str(e)}</div>',
            status=400
        )


@login_required
@admin_or_gso_required
@require_http_methods(["POST"])
@csrf_protect
def bulk_approve_requests_htmx(request):
    """HTMX endpoint for bulk approval of multiple requests"""
    request_ids = request.POST.getlist('request_ids')
    
    if not request_ids:
        return HttpResponse(
            '<div class="text-red-600">No requests selected for approval</div>',
            status=400
        )
    
    try:
        with transaction.atomic():
            approved_count = 0
            
            for request_id in request_ids:
                try:
                    supply_request = SupplyRequest.objects.get(id=request_id, status='pending')
                    
                    # Update request status
                    supply_request.status = 'approved'
                    supply_request.approved_by = request.user
                    supply_request.approved_date = timezone.now()
                    supply_request.save()
                    
                    # Auto-approve all items with requested quantities
                    for item in supply_request.items.all():
                        item.quantity_approved = item.quantity_requested
                        item.save()
                    
                    # Log the approval action
                    log_approval_action(request.user, supply_request, 'approved')
                    approved_count += 1
                    
                except SupplyRequest.DoesNotExist:
                    continue  # Skip if request doesn't exist or is not pending
            
            # Return success message and trigger page refresh
            return HttpResponse(
                f'<div class="text-green-600">Successfully approved {approved_count} requests</div>'
                '<script>setTimeout(() => window.location.reload(), 1500);</script>'
            )
            
    except Exception as e:
        return HttpResponse(
            f'<div class="text-red-600">Error during bulk approval: {str(e)}</div>',
            status=400
        )


@login_required
@admin_or_gso_required
def rejection_modal_htmx(request, request_id):
    """HTMX endpoint to return rejection modal HTML"""
    supply_request = get_object_or_404(SupplyRequest, id=request_id, status='pending')
    
    html = render_to_string('approvals/partials/rejection_modal.html', {
        'request': supply_request
    })
    
    return HttpResponse(html)


@login_required
@admin_or_gso_required
def request_detail_modal_htmx(request, request_id):
    """HTMX endpoint to return request detail modal HTML"""
    supply_request = get_object_or_404(
        SupplyRequest.objects.select_related('requester', 'approved_by').prefetch_related(
            'items__supply_item'
        ),
        id=request_id
    )
    
    html = render_to_string('approvals/partials/request_detail_modal.html', {
        'request': supply_request
    })
    
    return HttpResponse(html)


@login_required
@admin_or_gso_required
def pending_requests_table_htmx(request):
    """HTMX endpoint to refresh the pending requests table"""
    # Get search and filter parameters
    search_query = request.GET.get('search', '')
    department_filter = request.GET.get('department', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    
    # Base queryset with optimized queries
    requests = SupplyRequest.objects.filter(status='pending').select_related(
        'requester', 'requester__userprofile'
    ).prefetch_related(
        Prefetch('items', queryset=RequestItem.objects.select_related('supply_item'))
    ).order_by('-request_date')
    
    # Apply filters
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(requester__username__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query) |
            Q(department__icontains=search_query)
        )
    
    if department_filter:
        requests = requests.filter(department__icontains=department_filter)
    
    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    html = render_to_string('approvals/partials/requests_table.html', {
        'page_obj': page_obj
    })
    
    return HttpResponse(html)


@login_required
@admin_or_gso_required
def approval_history_view(request):
    """View for GSO staff to see approval history and audit trail"""
    # Get filter parameters
    action_filter = request.GET.get('action', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('search', '')
    
    # Get approval logs from the audit trail
    logs = get_approval_logs()
    
    # Apply filters
    if action_filter:
        logs = [log for log in logs if log['action'] == action_filter]
    
    if search_query:
        logs = [log for log in logs if 
                search_query.lower() in log['request_number'].lower() or
                search_query.lower() in log['requester'].lower() or
                search_query.lower() in log['approved_by'].lower()]
    
    if date_from:
        from datetime import datetime
        date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
        logs = [log for log in logs if log['approved_date'].date() >= date_from_obj]
    
    if date_to:
        from datetime import datetime
        date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
        logs = [log for log in logs if log['approved_date'].date() <= date_to_obj]
    
    # Pagination
    paginator = Paginator(logs, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'action_filter': action_filter,
        'date_from': date_from,
        'date_to': date_to,
        'search_query': search_query,
        'total_logs': len(logs)
    }
    
    return render(request, 'approvals/approval_history.html', context)


def log_approval_action(user, supply_request, action, reason=None):
    """Log approval/rejection actions for audit trail"""
    from django.core.cache import cache
    import json
    
    # Get existing logs from cache or initialize
    cache_key = 'approval_audit_logs'
    logs = cache.get(cache_key, [])
    
    # Create new log entry
    log_entry = {
        'id': len(logs) + 1,
        'request_id': supply_request.id,
        'request_number': supply_request.request_number,
        'requester': supply_request.requester.get_full_name() or supply_request.requester.username,
        'department': supply_request.department,
        'action': action,
        'approved_by': user.get_full_name() or user.username,
        'approved_date': timezone.now(),
        'reason': reason or '',
        'total_items': supply_request.total_items,
        'total_quantity': supply_request.total_quantity
    }
    
    # Add to beginning of logs list (most recent first)
    logs.insert(0, log_entry)
    
    # Keep only last 1000 logs to prevent memory issues
    logs = logs[:1000]
    
    # Save back to cache (expires in 30 days)
    cache.set(cache_key, logs, 60 * 60 * 24 * 30)


def get_approval_logs():
    """Get approval logs from cache"""
    from django.core.cache import cache
    
    cache_key = 'approval_audit_logs'
    logs = cache.get(cache_key, [])
    
    # If no cached logs, get from database (recent approvals/rejections)
    if not logs:
        recent_requests = SupplyRequest.objects.filter(
            status__in=['approved', 'rejected']
        ).select_related('requester', 'approved_by').order_by('-approved_date')[:100]
        
        logs = []
        for req in recent_requests:
            if req.approved_date and req.approved_by:
                logs.append({
                    'id': req.id,
                    'request_id': req.id,
                    'request_number': req.request_number,
                    'requester': req.requester.get_full_name() or req.requester.username,
                    'department': req.department,
                    'action': req.status,
                    'approved_by': req.approved_by.get_full_name() or req.approved_by.username,
                    'approved_date': req.approved_date,
                    'reason': req.rejection_reason or '',
                    'total_items': req.total_items,
                    'total_quantity': req.total_quantity
                })
        
        # Cache the logs
        cache.set(cache_key, logs, 60 * 60 * 24 * 30)
    
    return logs