from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator
import uuid


class UserProfile(models.Model):
    """Extended user profile with role-based access control"""
    ROLE_CHOICES = [
        ('admin', 'Admin'),
        ('gso_staff', 'GSO Staff'),
        ('department_user', 'Department User'),
    ]
    
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    role = models.CharField(max_length=20, choices=ROLE_CHOICES)
    department = models.CharField(max_length=100, blank=True)
    phone_number = models.CharField(max_length=15, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.user.username} - {self.get_role_display()}"
    
    class Meta:
        verbose_name = "User Profile"
        verbose_name_plural = "User Profiles"


class SupplyCategory(models.Model):
    """Categories for organizing supply items"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = "Supply Category"
        verbose_name_plural = "Supply Categories"
        ordering = ['name']


class SupplyItem(models.Model):
    """Individual supply items with QR code tracking"""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    category = models.ForeignKey(SupplyCategory, on_delete=models.CASCADE, related_name='items')
    unit_of_measure = models.CharField(max_length=50)
    current_stock = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    minimum_stock = models.IntegerField(default=10, validators=[MinValueValidator(0)])
    qr_code = models.ImageField(upload_to='qr_codes/', blank=True)
    qr_code_data = models.CharField(max_length=255, unique=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"{self.name} ({self.current_stock} {self.unit_of_measure})"
    
    def save(self, *args, **kwargs):
        # Check if this is an update and stock levels have changed
        is_update = self.pk is not None
        old_stock = None

        if is_update:
            try:
                old_instance = SupplyItem.objects.get(pk=self.pk)
                old_stock = old_instance.current_stock
            except SupplyItem.DoesNotExist:
                old_stock = None

        if not self.qr_code_data:
            self.qr_code_data = str(uuid.uuid4())
        super().save(*args, **kwargs)
    
    @property
    def is_low_stock(self):
        return self.current_stock <= self.minimum_stock
    
    @property
    def is_out_of_stock(self):
        return self.current_stock == 0
    
    class Meta:
        verbose_name = "Supply Item"
        verbose_name_plural = "Supply Items"
        ordering = ['name']


class SupplyRequest(models.Model):
    """Supply requests from department users"""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('approved', 'Approved'),
        ('released', 'Released'),
        ('rejected', 'Rejected'),
    ]
    
    request_number = models.CharField(max_length=20, unique=True, blank=True)
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='supply_requests')
    department = models.CharField(max_length=100)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')
    request_date = models.DateTimeField(auto_now_add=True)
    approved_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='approved_requests'
    )
    approved_date = models.DateTimeField(null=True, blank=True)
    rejection_reason = models.TextField(blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.request_number} - {self.requester.username} ({self.get_status_display()})"
    
    def save(self, *args, **kwargs):
        # Check if this is an update and status has changed
        is_update = self.pk is not None
        old_status = None

        if is_update:
            try:
                old_instance = SupplyRequest.objects.get(pk=self.pk)
                old_status = old_instance.status
            except SupplyRequest.DoesNotExist:
                old_status = None

        if not self.request_number:
            # Generate request number: REQ-YYYYMMDD-XXXX
            from django.utils import timezone
            date_str = timezone.now().strftime('%Y%m%d')
            last_request = SupplyRequest.objects.filter(
                request_number__startswith=f'REQ-{date_str}'
            ).order_by('-request_number').first()

            if last_request:
                last_num = int(last_request.request_number.split('-')[-1])
                new_num = last_num + 1
            else:
                new_num = 1

            self.request_number = f'REQ-{date_str}-{new_num:04d}'

        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        from django.urls import reverse
        return reverse('request_detail', kwargs={'pk': self.pk})
    
    @property
    def total_items(self):
        return self.items.count()
    
    @property
    def total_quantity(self):
        return sum(item.quantity_requested for item in self.items.all())
    
    @property
    def status_color(self):
        colors = {
            'pending': 'yellow',
            'approved': 'blue',
            'released': 'green',
            'rejected': 'red'
        }
        return colors.get(self.status, 'gray')
    
    class Meta:
        verbose_name = "Supply Request"
        verbose_name_plural = "Supply Requests"
        ordering = ['-request_date']


class RequestItem(models.Model):
    """Individual items within a supply request"""
    request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, related_name='items')
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE)
    quantity_requested = models.IntegerField(validators=[MinValueValidator(1)])
    quantity_approved = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    quantity_released = models.IntegerField(default=0, validators=[MinValueValidator(0)])
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.quantity_requested} requested"
    
    @property
    def is_fully_approved(self):
        return self.quantity_approved >= self.quantity_requested
    
    @property
    def is_fully_released(self):
        return self.quantity_released >= self.quantity_approved
    
    class Meta:
        verbose_name = "Request Item"
        verbose_name_plural = "Request Items"
        unique_together = ['request', 'supply_item']


class QRScanLog(models.Model):
    """Log of QR code scans for tracking supply movement"""
    SCAN_TYPE_CHOICES = [
        ('issuance', 'Issuance'),
        ('return', 'Return'),
        ('inventory_check', 'Inventory Check'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='scan_logs')
    scanned_by = models.ForeignKey(User, on_delete=models.CASCADE)
    scan_type = models.CharField(max_length=20, choices=SCAN_TYPE_CHOICES)
    scan_datetime = models.DateTimeField(auto_now_add=True)
    location = models.CharField(max_length=200, blank=True)
    notes = models.TextField(blank=True)
    request_item = models.ForeignKey(
        RequestItem, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='scan_logs'
    )
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.get_scan_type_display()} by {self.scanned_by.username}"
    
    class Meta:
        verbose_name = "QR Scan Log"
        verbose_name_plural = "QR Scan Logs"
        ordering = ['-scan_datetime']


class InventoryTransaction(models.Model):
    """Track all inventory movements and adjustments"""
    TRANSACTION_TYPE_CHOICES = [
        ('in', 'Stock In'),
        ('out', 'Stock Out'),
        ('adjustment', 'Adjustment'),
    ]
    
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPE_CHOICES)
    quantity = models.IntegerField()
    previous_stock = models.IntegerField()
    new_stock = models.IntegerField()
    performed_by = models.ForeignKey(User, on_delete=models.CASCADE)
    transaction_date = models.DateTimeField(auto_now_add=True)
    reference_number = models.CharField(max_length=50, blank=True)
    notes = models.TextField(blank=True)
    
    def __str__(self):
        return f"{self.supply_item.name} - {self.get_transaction_type_display()} ({self.quantity})"
    
    class Meta:
        verbose_name = "Inventory Transaction"
        verbose_name_plural = "Inventory Transactions"
        ordering = ['-transaction_date']


class SavedSearch(models.Model):
    """Saved search queries for quick access"""
    SEARCH_TYPE_CHOICES = [
        ('inventory', 'Inventory Items'),
        ('requests', 'Supply Requests'),
        ('scan_logs', 'QR Scan Logs'),
        ('users', 'Users'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='saved_searches')
    name = models.CharField(max_length=100)
    search_type = models.CharField(max_length=20, choices=SEARCH_TYPE_CHOICES)
    search_params = models.TextField()  # Store search parameters as JSON string
    is_public = models.BooleanField(default=False)  # Allow sharing with other users
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    usage_count = models.PositiveIntegerField(default=0)
    last_used = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-last_used', '-created_at']
        unique_together = ['user', 'name', 'search_type']
        indexes = [
            models.Index(fields=['user', 'search_type']),
            models.Index(fields=['is_public', 'search_type']),
        ]
        verbose_name = "Saved Search"
        verbose_name_plural = "Saved Searches"

    def __str__(self):
        return f"{self.name} ({self.get_search_type_display()}) - {self.user.username}"

    def increment_usage(self):
        """Increment usage count and update last used timestamp"""
        from django.utils import timezone
        self.usage_count += 1
        self.last_used = timezone.now()
        self.save(update_fields=['usage_count', 'last_used'])


class SystemConfiguration(models.Model):
    """System configuration and settings"""
    SETTING_TYPES = [
        ('string', 'String'),
        ('integer', 'Integer'),
        ('boolean', 'Boolean'),
        ('float', 'Float'),
        ('json', 'JSON'),
    ]

    key = models.CharField(max_length=100, unique=True)
    value = models.TextField()
    setting_type = models.CharField(max_length=20, choices=SETTING_TYPES, default='string')
    description = models.TextField(blank=True)
    category = models.CharField(max_length=50, default='general')
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    class Meta:
        ordering = ['category', 'key']
        verbose_name = "System Configuration"
        verbose_name_plural = "System Configurations"
        indexes = [
            models.Index(fields=['key']),
            models.Index(fields=['category', 'is_active']),
        ]

    def __str__(self):
        return f"{self.key} = {self.value}"

    def get_typed_value(self):
        """Return value converted to appropriate type"""
        if self.setting_type == 'integer':
            try:
                return int(self.value)
            except ValueError:
                return 0
        elif self.setting_type == 'boolean':
            return self.value.lower() in ('true', '1', 'yes', 'on')
        elif self.setting_type == 'float':
            try:
                return float(self.value)
            except ValueError:
                return 0.0
        elif self.setting_type == 'json':
            try:
                import json
                return json.loads(self.value)
            except (json.JSONDecodeError, ValueError):
                return {}
        else:
            return self.value

    @classmethod
    def get_setting(cls, key, default=None):
        """Get a setting value by key"""
        try:
            setting = cls.objects.get(key=key, is_active=True)
            return setting.get_typed_value()
        except cls.DoesNotExist:
            return default

    @classmethod
    def set_setting(cls, key, value, setting_type='string', description='', category='general', user=None):
        """Set a setting value"""
        setting, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'value': str(value),
                'setting_type': setting_type,
                'description': description,
                'category': category,
                'updated_by': user
            }
        )

        if not created:
            setting.value = str(value)
            setting.setting_type = setting_type
            setting.description = description
            setting.category = category
            setting.updated_by = user
            setting.save()

        return setting


class Notification(models.Model):
    """System notifications for users"""
    NOTIFICATION_TYPES = [
        ('low_stock', 'Low Stock Alert'),
        ('out_of_stock', 'Out of Stock Alert'),
        ('request_approved', 'Request Approved'),
        ('request_rejected', 'Request Rejected'),
        ('request_released', 'Request Released'),
        ('request_pending', 'Request Pending Approval'),
        ('system_alert', 'System Alert'),
        ('maintenance', 'Maintenance Notice'),
        ('general', 'General Notification'),
    ]

    PRIORITY_LEVELS = [
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    recipient = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPES)
    title = models.CharField(max_length=200)
    message = models.TextField()
    priority = models.CharField(max_length=10, choices=PRIORITY_LEVELS, default='normal')

    # Related objects
    supply_item = models.ForeignKey(SupplyItem, on_delete=models.CASCADE, null=True, blank=True)
    supply_request = models.ForeignKey(SupplyRequest, on_delete=models.CASCADE, null=True, blank=True)

    # Status and metadata
    is_read = models.BooleanField(default=False)
    is_dismissed = models.BooleanField(default=False)
    read_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)

    # Action URL for notification
    action_url = models.URLField(blank=True)
    action_text = models.CharField(max_length=50, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['notification_type', 'created_at']),
            models.Index(fields=['priority', 'created_at']),
            models.Index(fields=['expires_at']),
        ]
        verbose_name = "Notification"
        verbose_name_plural = "Notifications"

    def __str__(self):
        return f"{self.title} - {self.recipient.username}"

    def mark_as_read(self):
        """Mark notification as read"""
        if not self.is_read:
            from django.utils import timezone
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])

    def dismiss(self):
        """Dismiss notification"""
        self.is_dismissed = True
        self.save(update_fields=['is_dismissed'])

    def is_expired(self):
        """Check if notification has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False

    @classmethod
    def create_notification(cls, recipient, notification_type, title, message,
                          priority='normal', supply_item=None, supply_request=None,
                          action_url='', action_text='', expires_in_days=None):
        """Create a new notification"""
        from django.utils import timezone
        from datetime import timedelta

        expires_at = None
        if expires_in_days:
            expires_at = timezone.now() + timedelta(days=expires_in_days)

        return cls.objects.create(
            recipient=recipient,
            notification_type=notification_type,
            title=title,
            message=message,
            priority=priority,
            supply_item=supply_item,
            supply_request=supply_request,
            action_url=action_url,
            action_text=action_text,
            expires_at=expires_at
        )

    @classmethod
    def create_low_stock_alert(cls, supply_item, recipients=None):
        """Create low stock alert notifications"""
        if recipients is None:
            # Get admin and GSO staff users
            recipients = User.objects.filter(
                userprofile__role__in=['admin', 'gso_staff']
            )

        notifications = []
        for recipient in recipients:
            notification = cls.create_notification(
                recipient=recipient,
                notification_type='low_stock',
                title=f'Low Stock Alert: {supply_item.name}',
                message=f'{supply_item.name} is running low on stock. Current: {supply_item.current_stock} {supply_item.unit_of_measure}, Minimum: {supply_item.minimum_stock} {supply_item.unit_of_measure}',
                priority='high',
                supply_item=supply_item,
                action_url=f'/inventory/{supply_item.id}/',
                action_text='View Item',
                expires_in_days=7
            )
            notifications.append(notification)

        return notifications

    @classmethod
    def create_out_of_stock_alert(cls, supply_item, recipients=None):
        """Create out of stock alert notifications"""
        if recipients is None:
            # Get admin and GSO staff users
            recipients = User.objects.filter(
                userprofile__role__in=['admin', 'gso_staff']
            )

        notifications = []
        for recipient in recipients:
            notification = cls.create_notification(
                recipient=recipient,
                notification_type='out_of_stock',
                title=f'Out of Stock: {supply_item.name}',
                message=f'{supply_item.name} is completely out of stock and needs immediate restocking.',
                priority='urgent',
                supply_item=supply_item,
                action_url=f'/inventory/{supply_item.id}/',
                action_text='Restock Now',
                expires_in_days=3
            )
            notifications.append(notification)

        return notifications

    @classmethod
    def create_request_status_notification(cls, supply_request, old_status, new_status):
        """Create notification for request status changes"""
        status_messages = {
            'approved': f'Your supply request {supply_request.request_number} has been approved.',
            'rejected': f'Your supply request {supply_request.request_number} has been rejected.',
            'released': f'Your supply request {supply_request.request_number} has been released and is ready for pickup.',
        }

        if new_status in status_messages:
            notification_type = f'request_{new_status}'
            title = f'Request {new_status.title()}: {supply_request.request_number}'
            message = status_messages[new_status]

            if supply_request.rejection_reason and new_status == 'rejected':
                message += f' Reason: {supply_request.rejection_reason}'

            priority = 'high' if new_status == 'rejected' else 'normal'

            return cls.create_notification(
                recipient=supply_request.requester,
                notification_type=notification_type,
                title=title,
                message=message,
                priority=priority,
                supply_request=supply_request,
                action_url=f'/requests/{supply_request.id}/',
                action_text='View Request',
                expires_in_days=30
            )

        return None


class NotificationPreference(models.Model):
    """User notification preferences"""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='notification_preferences')

    # Notification type preferences
    low_stock_alerts = models.BooleanField(default=True)
    out_of_stock_alerts = models.BooleanField(default=True)
    request_status_updates = models.BooleanField(default=True)
    system_alerts = models.BooleanField(default=True)
    maintenance_notices = models.BooleanField(default=True)

    # Delivery preferences
    in_app_notifications = models.BooleanField(default=True)
    email_notifications = models.BooleanField(default=False)

    # Frequency settings
    digest_frequency = models.CharField(
        max_length=20,
        choices=[
            ('immediate', 'Immediate'),
            ('hourly', 'Hourly'),
            ('daily', 'Daily'),
            ('weekly', 'Weekly'),
        ],
        default='immediate'
    )

    # Quiet hours
    quiet_hours_enabled = models.BooleanField(default=False)
    quiet_hours_start = models.TimeField(null=True, blank=True)
    quiet_hours_end = models.TimeField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Notification Preference"
        verbose_name_plural = "Notification Preferences"

    def __str__(self):
        return f"Notification preferences for {self.user.username}"

    def should_receive_notification(self, notification_type):
        """Check if user should receive a specific type of notification"""
        type_mapping = {
            'low_stock': self.low_stock_alerts,
            'out_of_stock': self.out_of_stock_alerts,
            'request_approved': self.request_status_updates,
            'request_rejected': self.request_status_updates,
            'request_released': self.request_status_updates,
            'system_alert': self.system_alerts,
            'maintenance': self.maintenance_notices,
        }

        return type_mapping.get(notification_type, True)

    def is_in_quiet_hours(self):
        """Check if current time is within quiet hours"""
        from django.utils import timezone

        if not self.quiet_hours_enabled or not self.quiet_hours_start or not self.quiet_hours_end:
            return False

        now = timezone.now().time()
        start = self.quiet_hours_start
        end = self.quiet_hours_end

        if start <= end:
            return start <= now <= end
        else:  # Quiet hours span midnight
            return now >= start or now <= end
