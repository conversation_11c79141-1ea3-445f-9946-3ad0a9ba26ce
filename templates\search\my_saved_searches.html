{% extends 'base.html' %}
{% load static %}

{% block title %}My Saved Searches{% endblock %}
{% block page_title %}My Saved Searches{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">My Saved Searches</h1>
            <p class="text-gray-600">Quick access to your frequently used search queries</p>
        </div>
        <div class="flex space-x-2">
            <a href="{% url 'advanced_inventory_search' %}" 
               class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                New Search
            </a>
        </div>
    </div>

    <!-- Filter Tabs -->
    <div class="mb-6">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8">
                <a href="{% url 'my_saved_searches' %}" 
                   class="{% if not search_type_filter %}border-blue-500 text-blue-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                    All Searches
                </a>
                {% for value, label in search_type_choices %}
                    <a href="{% url 'my_saved_searches' %}?type={{ value }}" 
                       class="{% if search_type_filter == value %}border-blue-500 text-blue-600{% else %}border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300{% endif %} whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                        {{ label }}
                    </a>
                {% endfor %}
            </nav>
        </div>
    </div>

    <!-- Saved Searches -->
    <div id="saved-searches-list">
        {% include 'search/partials/saved_searches_list.html' %}
    </div>
</div>
{% endblock %}
