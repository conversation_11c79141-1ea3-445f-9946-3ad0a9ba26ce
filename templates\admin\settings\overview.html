{% extends 'admin_dashboard.html' %}

{% block title %}System Settings - Admin Dashboard{% endblock %}
{% block page_title %}System Settings{% endblock %}

{% block content %}
<div class="space-y-6">
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">System Configuration</h3>
        <p class="text-gray-600 mb-6">Manage system-wide settings and configurations.</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <a href="{% url 'admin_settings_general' %}" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-gray-900">General</h4>
                        <p class="text-sm text-gray-600">Basic system settings</p>
                    </div>
                </div>
            </a>
            
            <a href="{% url 'admin_settings_email' %}" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-green-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-gray-900">Email</h4>
                        <p class="text-sm text-gray-600">Email configuration</p>
                    </div>
                </div>
            </a>
            
            <a href="{% url 'admin_settings_backup' %}" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-purple-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-gray-900">Backup</h4>
                        <p class="text-sm text-gray-600">Backup settings</p>
                    </div>
                </div>
            </a>
            
            <a href="{% url 'admin_settings_security' %}" class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-red-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                    </svg>
                    <div>
                        <h4 class="font-medium text-gray-900">Security</h4>
                        <p class="text-sm text-gray-600">Security settings</p>
                    </div>
                </div>
            </a>
        </div>
    </div>
    
    {% if settings_by_category %}
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Current Settings</h3>
        {% for category, settings in settings_by_category.items %}
        <div class="mb-6">
            <h4 class="text-md font-medium text-gray-900 mb-2">{{ category|title }}</h4>
            <div class="space-y-2">
                {% for setting in settings %}
                <div class="flex justify-between items-center py-2 border-b border-gray-100">
                    <span class="text-sm font-medium text-gray-700">{{ setting.key }}</span>
                    <span class="text-sm text-gray-600">{{ setting.value|truncatechars:50 }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
</div>
{% endblock %}
