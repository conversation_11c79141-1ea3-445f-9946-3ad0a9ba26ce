/**
 * Touch Validation Script
 * Validates that the mobile sidebar toggle works with single tap/click
 */

class TouchValidation {
    constructor() {
        this.testResults = [];
        this.init();
    }

    init() {
        console.log('🔍 Touch Validation Script Loaded');
        this.addValidationUI();
        this.runInitialValidation();
    }

    /**
     * Add validation UI to the page
     */
    addValidationUI() {
        // Create validation panel
        const panel = document.createElement('div');
        panel.id = 'touch-validation-panel';
        panel.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 2px solid #3b82f6;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            z-index: 9999;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        `;

        panel.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 10px; color: #3b82f6;">
                📱 Touch Validation
            </div>
            <button id="run-touch-test" style="
                background: #3b82f6;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                cursor: pointer;
                margin-bottom: 10px;
                width: 100%;
            ">Run Touch Test</button>
            <button id="toggle-validation-panel" style="
                background: #6b7280;
                color: white;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                cursor: pointer;
                margin-bottom: 10px;
                width: 100%;
                font-size: 10px;
            ">Hide Panel</button>
            <div id="validation-results" style="
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                padding: 8px;
                max-height: 200px;
                overflow-y: auto;
            "></div>
        `;

        document.body.appendChild(panel);

        // Add event listeners
        document.getElementById('run-touch-test').addEventListener('click', () => {
            this.runComprehensiveTest();
        });

        document.getElementById('toggle-validation-panel').addEventListener('click', () => {
            const resultsDiv = document.getElementById('validation-results');
            const button = document.getElementById('toggle-validation-panel');
            if (resultsDiv.style.display === 'none') {
                resultsDiv.style.display = 'block';
                button.textContent = 'Hide Panel';
            } else {
                resultsDiv.style.display = 'none';
                button.textContent = 'Show Panel';
            }
        });
    }

    /**
     * Run initial validation on page load
     */
    runInitialValidation() {
        setTimeout(() => {
            this.validateToggleButton();
            this.validateEventHandlers();
            this.validateResponsiveDesign();
            this.displayResults();
        }, 1000);
    }

    /**
     * Validate toggle button exists and is properly configured
     */
    validateToggleButton() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        
        if (!toggleBtn) {
            this.addResult('❌', 'Toggle button not found');
            return;
        }

        this.addResult('✅', 'Toggle button found');

        // Check if button has proper attributes
        const hasAriaLabel = toggleBtn.hasAttribute('aria-label');
        const hasAriaExpanded = toggleBtn.hasAttribute('aria-expanded');
        const hasType = toggleBtn.getAttribute('type') === 'button';

        this.addResult(hasAriaLabel ? '✅' : '❌', `ARIA label: ${hasAriaLabel}`);
        this.addResult(hasAriaExpanded ? '✅' : '❌', `ARIA expanded: ${hasAriaExpanded}`);
        this.addResult(hasType ? '✅' : '❌', `Button type: ${hasType}`);

        // Check CSS properties
        const styles = window.getComputedStyle(toggleBtn);
        const hasProperSize = parseInt(styles.minWidth) >= 44 && parseInt(styles.minHeight) >= 44;
        const hasTouchAction = styles.touchAction === 'manipulation';

        this.addResult(hasProperSize ? '✅' : '❌', `Touch target size: ${hasProperSize}`);
        this.addResult(hasTouchAction ? '✅' : '❌', `Touch action: ${hasTouchAction}`);
    }

    /**
     * Validate event handlers
     */
    validateEventHandlers() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (!toggleBtn) return;

        // Check for Alpine.js click handler
        const hasClickHandler = toggleBtn.hasAttribute('@click') || toggleBtn.onclick;
        this.addResult(hasClickHandler ? '✅' : '❌', `Click handler: ${hasClickHandler}`);

        // Check for conflicting touch handlers
        const hasTouchStart = toggleBtn.hasAttribute('@touchstart');
        this.addResult(!hasTouchStart ? '✅' : '❌', `No conflicting touchstart: ${!hasTouchStart}`);
    }

    /**
     * Validate responsive design
     */
    validateResponsiveDesign() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (!toggleBtn) return;

        // Test at mobile breakpoint
        const originalWidth = window.innerWidth;
        
        // Simulate mobile viewport
        Object.defineProperty(window, 'innerWidth', { value: 375, writable: true });
        window.dispatchEvent(new Event('resize'));
        
        setTimeout(() => {
            const mobileVisible = window.getComputedStyle(toggleBtn).display !== 'none';
            this.addResult(mobileVisible ? '✅' : '❌', `Mobile visibility: ${mobileVisible}`);
            
            // Restore original width
            Object.defineProperty(window, 'innerWidth', { value: originalWidth, writable: true });
            window.dispatchEvent(new Event('resize'));
        }, 100);
    }

    /**
     * Run comprehensive touch test
     */
    async runComprehensiveTest() {
        this.clearResults();
        this.addResult('🧪', 'Running comprehensive touch test...');

        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const sidebar = document.querySelector('.custom-sidebar');

        if (!toggleBtn || !sidebar) {
            this.addResult('❌', 'Required elements not found');
            return;
        }

        // Test 1: Single click
        this.addResult('🔄', 'Testing single click...');
        toggleBtn.click();
        await this.wait(500);

        const openedByClick = sidebar.classList.contains('translate-x-0') || 
                             sidebar.classList.contains('open');
        this.addResult(openedByClick ? '✅' : '❌', `Single click opens: ${openedByClick}`);

        // Close sidebar
        if (openedByClick) {
            toggleBtn.click();
            await this.wait(500);
        }

        // Test 2: Touch simulation
        this.addResult('🔄', 'Testing touch events...');
        
        // Simulate touch
        const touchStart = new TouchEvent('touchstart', {
            bubbles: true,
            touches: [{ clientX: 100, clientY: 100 }]
        });
        
        const touchEnd = new TouchEvent('touchend', {
            bubbles: true,
            changedTouches: [{ clientX: 100, clientY: 100 }]
        });

        toggleBtn.dispatchEvent(touchStart);
        await this.wait(50);
        toggleBtn.dispatchEvent(touchEnd);
        
        // Then trigger click (as browsers do)
        toggleBtn.click();
        await this.wait(500);

        const openedByTouch = sidebar.classList.contains('translate-x-0') || 
                             sidebar.classList.contains('open');
        this.addResult(openedByTouch ? '✅' : '❌', `Touch opens: ${openedByTouch}`);

        // Test 3: Rapid clicks (should not cause issues)
        this.addResult('🔄', 'Testing rapid clicks...');
        
        // Close sidebar first
        if (openedByTouch) {
            toggleBtn.click();
            await this.wait(300);
        }

        // Rapid clicks
        toggleBtn.click();
        toggleBtn.click();
        toggleBtn.click();
        await this.wait(500);

        const stableAfterRapid = sidebar.classList.contains('translate-x-0') || 
                                sidebar.classList.contains('open');
        this.addResult('✅', `Stable after rapid clicks: ${stableAfterRapid}`);

        this.addResult('🎉', 'Touch test completed!');
    }

    /**
     * Add test result
     */
    addResult(icon, message) {
        this.testResults.push({ icon, message, timestamp: new Date().toLocaleTimeString() });
        this.displayResults();
    }

    /**
     * Clear test results
     */
    clearResults() {
        this.testResults = [];
        this.displayResults();
    }

    /**
     * Display results in the UI
     */
    displayResults() {
        const resultsDiv = document.getElementById('validation-results');
        if (!resultsDiv) return;

        resultsDiv.innerHTML = this.testResults.map(result => 
            `<div style="margin-bottom: 4px;">
                <span style="margin-right: 8px;">${result.icon}</span>
                <span style="font-size: 11px; color: #6b7280;">[${result.timestamp}]</span>
                <span>${result.message}</span>
            </div>`
        ).join('');

        // Scroll to bottom
        resultsDiv.scrollTop = resultsDiv.scrollHeight;
    }

    /**
     * Wait helper
     */
    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize validation when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    // Only run in development or when explicitly requested
    if (window.location.search.includes('validate=true') || 
        window.location.hostname === 'localhost' || 
        window.location.hostname === '127.0.0.1') {
        new TouchValidation();
    }
});

// Export for manual use
window.TouchValidation = TouchValidation;
