#!/usr/bin/env python
"""
Quick test to verify QR scanner page loads correctly
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from suptrack.models import UserProfile

def test_qr_scanner_access():
    """Test QR scanner page access"""
    print("Testing QR Scanner Page Access...")
    
    client = Client()
    
    # Create a GSO staff user for testing
    user, created = User.objects.get_or_create(
        username='test_gso',
        defaults={
            'password': 'testpass123',
            'first_name': 'Test',
            'last_name': 'GSO'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
    
    profile, created = UserProfile.objects.get_or_create(
        user=user,
        defaults={
            'role': 'gso_staff',
            'department': 'GSO'
        }
    )
    
    # Test login
    login_success = client.login(username='test_gso', password='testpass123')
    
    if not login_success:
        print("❌ Login failed")
        return False
    
    print("✅ Login successful")
    
    # Test QR scanner page access
    try:
        response = client.get('/qr-scanner/')
        
        if response.status_code == 200:
            print("✅ QR Scanner page loads successfully")
            print(f"   Status Code: {response.status_code}")
            
            # Check if page contains expected content
            content = response.content.decode('utf-8')
            if 'QR Code Scanner' in content:
                print("✅ Page contains expected QR Scanner content")
            else:
                print("⚠️  Page loaded but missing expected content")
            
            if 'Start Camera' in content:
                print("✅ Camera controls are present")
            else:
                print("⚠️  Camera controls missing")
                
            return True
        else:
            print(f"❌ QR Scanner page failed to load: Status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing QR scanner page: {e}")
        return False

if __name__ == '__main__':
    success = test_qr_scanner_access()
    
    if success:
        print("\n🎉 QR Scanner page is working correctly!")
        print("✅ Middleware issue resolved")
        print("✅ Page loads without errors")
        print("✅ Authentication working")
        print("✅ Role-based access control working")
    else:
        print("\n❌ QR Scanner page has issues")
        
    sys.exit(0 if success else 1)
