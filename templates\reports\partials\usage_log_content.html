<!-- Summary Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-blue-600">{{ summary_stats.total_transactions }}</p>
            <p class="text-sm text-gray-600">Total Transactions</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ summary_stats.stock_in_transactions }}</p>
            <p class="text-sm text-gray-600">Stock In</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-red-600">{{ summary_stats.stock_out_transactions }}</p>
            <p class="text-sm text-gray-600">Stock Out</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-purple-600">{{ summary_stats.adjustment_transactions }}</p>
            <p class="text-sm text-gray-600">Adjustments</p>
        </div>
    </div>
</div>

<!-- Analytics Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Most Active Users -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Most Active Users</h3>
        <div class="space-y-3">
            {% for user in active_users %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">
                        {{ user.performed_by__first_name }} {{ user.performed_by__last_name }}
                        {% if not user.performed_by__first_name %}{{ user.performed_by__username }}{% endif %}
                    </p>
                    <p class="text-sm text-gray-600">{{ user.transaction_count }} transactions</p>
                </div>
                <div class="text-right text-sm">
                    <p class="text-gray-900">{{ user.total_quantity }} total qty</p>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No user activity data available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Most Active Items -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Most Active Items</h3>
        <div class="space-y-3">
            {% for item in active_items %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ item.supply_item__name }}</p>
                    <p class="text-sm text-gray-600">{{ item.transaction_count }} transactions</p>
                </div>
                <div class="text-right text-xs">
                    <p class="text-green-600">In: {{ item.total_quantity_in|default:0 }}</p>
                    <p class="text-red-600">Out: {{ item.total_quantity_out|default:0 }}</p>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No item activity data available</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Detailed Transaction List -->
<div class="bg-white rounded-lg shadow-md">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Transaction Details</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock Change</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Performed By</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reference</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for transaction in page_obj %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ transaction.transaction_date|date:"M d, Y H:i" }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <a href="{% url 'supply_item_detail' transaction.supply_item.id %}" class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ transaction.supply_item.name }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                            {% if transaction.transaction_type == 'in' %}bg-green-100 text-green-800
                            {% elif transaction.transaction_type == 'out' %}bg-red-100 text-red-800
                            {% else %}bg-blue-100 text-blue-800{% endif %}">
                            {{ transaction.get_transaction_type_display }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {{ transaction.quantity }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ transaction.previous_stock }} → {{ transaction.new_stock }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ transaction.performed_by.get_full_name|default:transaction.performed_by.username }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ transaction.reference_number|default:"-" }}
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        No transactions found matching the current filters.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex space-x-1">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
