"""
Unit tests for admin navigation functionality.
"""

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse, NoReverseMatch
from django.template import Template, Context
from suptrack.models import UserProfile
from suptrack.context_processors import get_admin_navigation_items, navigation_context


class AdminNavigationTestCase(TestCase):
    """Test admin navigation functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.admin_user, role='admin')
        
        # Create regular user
        self.regular_user = User.objects.create_user(
            username='user_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.regular_user, role='department_user')
    
    def test_admin_navigation_url_resolution(self):
        """Test that admin navigation URLs can be resolved."""
        # Core admin URLs that should exist
        core_admin_urls = [
            'admin_dashboard',
            'admin_users',
            'admin_users_list',
            'admin_users_add',
            'admin_permissions',
            'admin_roles_management',
            'admin_settings',
            'admin_settings_general',
            'admin_settings_email',
            'admin_settings_backup',
            'admin_settings_security',
            'admin_inventory',
            'admin_requests',
            'admin_bulk_operations',
            'admin_notifications',
            'admin_notifications_list',
            'create_system_notification',
            'admin_notification_templates',
            'admin_notification_settings',
            'admin_system_health',
            'admin_audit_logs',
            'admin_performance_reports'
        ]
        
        resolved_urls = []
        failed_urls = []
        
        for url_name in core_admin_urls:
            try:
                url = reverse(url_name)
                resolved_urls.append((url_name, url))
            except NoReverseMatch:
                failed_urls.append(url_name)
        
        # Assert that most core URLs resolve
        self.assertGreater(len(resolved_urls), len(failed_urls), 
                          f"More URLs failed than succeeded. Failed: {failed_urls}")
        
        # Print results for debugging
        print(f"\nResolved URLs: {len(resolved_urls)}")
        print(f"Failed URLs: {len(failed_urls)}")
        if failed_urls:
            print(f"Failed URL names: {failed_urls}")
    
    def test_admin_navigation_context_processor(self):
        """Test admin navigation context processor."""
        from django.test import RequestFactory
        
        factory = RequestFactory()
        request = factory.get('/admin-dashboard/')
        request.user = self.admin_user
        
        context = navigation_context(request)
        
        # Check that admin role is detected
        self.assertEqual(context['user_role'], 'admin')
        
        # Check that navigation items are generated
        self.assertIsInstance(context['navigation_items'], list)
        
        # Check that breadcrumbs are generated
        self.assertIsInstance(context['breadcrumbs'], list)
    
    def test_admin_navigation_items_generation(self):
        """Test admin navigation items generation."""
        try:
            admin_items = get_admin_navigation_items()
            
            # Should return a list
            self.assertIsInstance(admin_items, list)
            
            # Should have some items
            self.assertGreater(len(admin_items), 0)
            
            # Each item should have required fields
            for item in admin_items:
                self.assertIn('name', item)
                self.assertIn('url', item)
                self.assertIn('icon', item)
                self.assertIn('section', item)
                
        except Exception as e:
            self.fail(f"Admin navigation items generation failed: {e}")
    
    def test_admin_dashboard_access(self):
        """Test admin dashboard access control."""
        # Test unauthenticated access
        response = self.client.get(reverse('admin_dashboard'))
        self.assertIn(response.status_code, [302, 403])  # Should redirect or deny
        
        # Test regular user access
        self.client.login(username='user_test', password='testpass123')
        response = self.client.get(reverse('admin_dashboard'))
        self.assertIn(response.status_code, [302, 403])  # Should redirect or deny
        
        # Test admin user access
        self.client.login(username='admin_test', password='testpass123')
        response = self.client.get(reverse('admin_dashboard'))
        self.assertIn(response.status_code, [200, 302])  # Should allow access
    
    def test_admin_sidebar_template_rendering(self):
        """Test that admin sidebar template renders without errors."""
        self.client.login(username='admin_test', password='testpass123')
        
        # Test admin dashboard page
        response = self.client.get(reverse('admin_dashboard'))
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for admin-specific elements
            self.assertIn('Admin Panel', content)
            self.assertIn('User Management', content)
            self.assertIn('System Settings', content)
            
        else:
            # If not 200, at least it shouldn't be a server error
            self.assertNotEqual(response.status_code, 500)
    
    def test_admin_navigation_links(self):
        """Test that admin navigation links are accessible."""
        self.client.login(username='admin_test', password='testpass123')
        
        # Test key admin pages
        admin_pages = [
            'admin_users',
            'admin_settings',
            'admin_notifications',
            'admin_system_health'
        ]
        
        accessible_pages = []
        inaccessible_pages = []
        
        for page_name in admin_pages:
            try:
                url = reverse(page_name)
                response = self.client.get(url)
                
                if response.status_code in [200, 302]:
                    accessible_pages.append(page_name)
                else:
                    inaccessible_pages.append((page_name, response.status_code))
                    
            except NoReverseMatch:
                inaccessible_pages.append((page_name, 'NoReverseMatch'))
        
        # Most pages should be accessible
        self.assertGreater(len(accessible_pages), 0, 
                          "No admin pages were accessible")
        
        print(f"\nAccessible admin pages: {len(accessible_pages)}")
        print(f"Inaccessible admin pages: {len(inaccessible_pages)}")
        
        if inaccessible_pages:
            print(f"Inaccessible pages: {inaccessible_pages}")
    
    def test_admin_sidebar_navigation_structure(self):
        """Test the structure of admin sidebar navigation."""
        try:
            admin_items = get_admin_navigation_items()
            
            # Check for expected main sections
            expected_sections = [
                'Dashboard',
                'User Management', 
                'System Settings',
                'Request Management',
                'Reports & Analytics',
                'Notifications'
            ]
            
            found_sections = [item['name'] for item in admin_items]
            
            for section in expected_sections:
                if section not in found_sections:
                    print(f"Warning: Expected section '{section}' not found in navigation")
            
            # Check that at least half of expected sections are present
            found_count = sum(1 for section in expected_sections if section in found_sections)
            self.assertGreaterEqual(found_count, len(expected_sections) // 2,
                                   f"Too few expected sections found. Found: {found_sections}")
            
        except Exception as e:
            self.fail(f"Admin navigation structure test failed: {e}")


class AdminNavigationIntegrationTestCase(TestCase):
    """Integration tests for admin navigation."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        self.admin_user = User.objects.create_user(
            username='admin_integration',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.admin_user, role='admin')
    
    def test_admin_navigation_end_to_end(self):
        """Test admin navigation from login to page access."""
        # Login as admin
        login_success = self.client.login(username='admin_integration', password='testpass123')
        self.assertTrue(login_success, "Admin login failed")
        
        # Access admin dashboard
        response = self.client.get(reverse('admin_dashboard'))
        self.assertIn(response.status_code, [200, 302], 
                     f"Admin dashboard access failed: {response.status_code}")
        
        # Test navigation to different admin sections
        admin_sections = ['admin_users', 'admin_settings', 'admin_notifications']
        
        for section in admin_sections:
            try:
                url = reverse(section)
                response = self.client.get(url)
                self.assertIn(response.status_code, [200, 302], 
                             f"Failed to access {section}: {response.status_code}")
            except NoReverseMatch:
                print(f"Warning: URL {section} not found")
    
    def test_admin_htmx_endpoints(self):
        """Test admin HTMX endpoints."""
        self.client.login(username='admin_integration', password='testpass123')
        
        htmx_endpoints = [
            'admin_users_count',
            'admin_inventory_alerts_count',
            'admin_pending_requests_count',
            'admin_notification_alerts_count',
            'admin_audit_alerts_count',
            'admin_system_health_indicator',
            'admin_quick_stats_htmx'
        ]
        
        working_endpoints = []
        failing_endpoints = []
        
        for endpoint in htmx_endpoints:
            try:
                url = reverse(endpoint)
                response = self.client.get(url)
                
                if response.status_code == 200:
                    working_endpoints.append(endpoint)
                else:
                    failing_endpoints.append((endpoint, response.status_code))
                    
            except NoReverseMatch:
                failing_endpoints.append((endpoint, 'NoReverseMatch'))
        
        print(f"\nWorking HTMX endpoints: {len(working_endpoints)}")
        print(f"Failing HTMX endpoints: {len(failing_endpoints)}")
        
        # At least some HTMX endpoints should work
        self.assertGreater(len(working_endpoints), 0, 
                          "No HTMX endpoints are working")
