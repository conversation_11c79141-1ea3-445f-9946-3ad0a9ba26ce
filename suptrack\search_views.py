"""
Advanced search and filtering views
"""
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, F, Count, Sum, Avg
from django.utils import timezone
from datetime import datetime, timedelta
import json

from .models import (
    SupplyRequest, RequestItem, SupplyItem, SupplyCategory,
    InventoryTransaction, QRScanLog, UserProfile, SavedSearch
)
from .decorators import role_required


@login_required
def advanced_inventory_search(request):
    """Advanced search for inventory items with multiple filters"""
    # Get search parameters
    query = request.GET.get('q', '').strip()
    category_id = request.GET.get('category', '')
    stock_status = request.GET.get('stock_status', '')
    price_min = request.GET.get('price_min', '')
    price_max = request.GET.get('price_max', '')
    date_added_from = request.GET.get('date_added_from', '')
    date_added_to = request.GET.get('date_added_to', '')
    sort_by = request.GET.get('sort', 'name')
    page = request.GET.get('page', 1)
    
    # Base queryset
    items = SupplyItem.objects.select_related('category').annotate(
        total_requests=Count('requestitem'),
        total_transactions=Count('transactions'),
        avg_monthly_usage=Avg('transactions__quantity')
    )
    
    # Apply text search
    if query:
        items = items.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query) |
            Q(category__name__icontains=query) |
            Q(qr_code_data__icontains=query)
        )
    
    # Apply category filter
    if category_id:
        items = items.filter(category_id=category_id)
    
    # Apply stock status filter
    if stock_status == 'out_of_stock':
        items = items.filter(current_stock=0)
    elif stock_status == 'low_stock':
        items = items.filter(current_stock__lte=F('minimum_stock'), current_stock__gt=0)
    elif stock_status == 'normal':
        items = items.filter(current_stock__gt=F('minimum_stock'))
    elif stock_status == 'overstocked':
        items = items.filter(current_stock__gt=F('minimum_stock') * 2)
    
    # Apply date filters
    if date_added_from:
        items = items.filter(created_at__date__gte=date_added_from)
    if date_added_to:
        items = items.filter(created_at__date__lte=date_added_to)
    
    # Apply sorting
    sort_options = {
        'name': 'name',
        'name_desc': '-name',
        'category': 'category__name',
        'stock_asc': 'current_stock',
        'stock_desc': '-current_stock',
        'usage_desc': '-total_requests',
        'updated': '-updated_at',
        'created': '-created_at'
    }
    items = items.order_by(sort_options.get(sort_by, 'name'))
    
    # Pagination
    paginator = Paginator(items, 20)
    page_obj = paginator.get_page(page)
    
    # Get categories for filter dropdown
    categories = SupplyCategory.objects.all().order_by('name')
    
    context = {
        'page_obj': page_obj,
        'categories': categories,
        'search_params': {
            'q': query,
            'category': category_id,
            'stock_status': stock_status,
            'price_min': price_min,
            'price_max': price_max,
            'date_added_from': date_added_from,
            'date_added_to': date_added_to,
            'sort': sort_by,
        },
        'total_results': paginator.count,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'search/partials/inventory_results.html', context)
    
    return render(request, 'search/advanced_inventory_search.html', context)


@login_required
def advanced_request_search(request):
    """Advanced search for supply requests with comprehensive filters"""
    # Get search parameters
    query = request.GET.get('q', '').strip()
    status = request.GET.get('status', '')
    department = request.GET.get('department', '')
    requester_id = request.GET.get('requester', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    priority = request.GET.get('priority', '')
    has_items = request.GET.get('has_items', '')
    sort_by = request.GET.get('sort', '-request_date')
    page = request.GET.get('page', 1)
    
    # Base queryset
    requests = SupplyRequest.objects.select_related(
        'requester', 'approved_by'
    ).prefetch_related('items__supply_item')
    
    # Apply role-based filtering
    if hasattr(request.user, 'userprofile'):
        if request.user.userprofile.role == 'department_user':
            requests = requests.filter(requester=request.user)
    
    # Apply text search
    if query:
        requests = requests.filter(
            Q(request_number__icontains=query) |
            Q(requester__username__icontains=query) |
            Q(requester__first_name__icontains=query) |
            Q(requester__last_name__icontains=query) |
            Q(department__icontains=query) |
            Q(items__supply_item__name__icontains=query)
        ).distinct()
    
    # Apply status filter
    if status:
        requests = requests.filter(status=status)
    
    # Apply department filter
    if department:
        requests = requests.filter(department__icontains=department)
    
    # Apply requester filter
    if requester_id:
        requests = requests.filter(requester_id=requester_id)
    
    # Apply date filters
    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    
    # Apply item count filter
    if has_items == 'with_items':
        requests = requests.filter(items__isnull=False).distinct()
    elif has_items == 'no_items':
        requests = requests.filter(items__isnull=True)
    
    # Apply sorting
    sort_options = {
        'request_date': '-request_date',
        'request_date_asc': 'request_date',
        'request_number': 'request_number',
        'requester': 'requester__last_name',
        'department': 'department',
        'status': 'status',
        'item_count': 'request_number',
        'approved_date': '-approved_date'
    }
    requests = requests.order_by(sort_options.get(sort_by, '-request_date'))
    
    # Pagination
    paginator = Paginator(requests, 15)
    page_obj = paginator.get_page(page)
    
    # Get filter options
    departments = SupplyRequest.objects.values_list('department', flat=True).distinct().order_by('department')
    requesters = UserProfile.objects.select_related('user').all()
    
    context = {
        'page_obj': page_obj,
        'departments': departments,
        'requesters': requesters,
        'search_params': {
            'q': query,
            'status': status,
            'department': department,
            'requester': requester_id,
            'date_from': date_from,
            'date_to': date_to,
            'priority': priority,
            'has_items': has_items,
            'sort': sort_by,
        },
        'status_choices': SupplyRequest.STATUS_CHOICES,
        'total_results': paginator.count,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'search/partials/request_results.html', context)
    
    return render(request, 'search/advanced_request_search.html', context)


@login_required
@role_required(['admin', 'gso_staff'])
def advanced_scan_log_search(request):
    """Advanced search for QR scan logs with detailed filtering"""
    # Get search parameters
    query = request.GET.get('q', '').strip()
    scan_type = request.GET.get('scan_type', '')
    user_id = request.GET.get('user', '')
    item_id = request.GET.get('item', '')
    location = request.GET.get('location', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    time_from = request.GET.get('time_from', '')
    time_to = request.GET.get('time_to', '')
    sort_by = request.GET.get('sort', '-scan_datetime')
    page = request.GET.get('page', 1)
    
    # Base queryset
    scan_logs = QRScanLog.objects.select_related(
        'supply_item', 'scanned_by', 'request_item__request'
    ).all()
    
    # Apply text search
    if query:
        scan_logs = scan_logs.filter(
            Q(supply_item__name__icontains=query) |
            Q(location__icontains=query) |
            Q(notes__icontains=query) |
            Q(scanned_by__username__icontains=query) |
            Q(scanned_by__first_name__icontains=query) |
            Q(scanned_by__last_name__icontains=query)
        )
    
    # Apply scan type filter
    if scan_type:
        scan_logs = scan_logs.filter(scan_type=scan_type)
    
    # Apply user filter
    if user_id:
        scan_logs = scan_logs.filter(scanned_by_id=user_id)
    
    # Apply item filter
    if item_id:
        scan_logs = scan_logs.filter(supply_item_id=item_id)
    
    # Apply location filter
    if location:
        scan_logs = scan_logs.filter(location__icontains=location)
    
    # Apply date filters
    if date_from:
        scan_logs = scan_logs.filter(scan_datetime__date__gte=date_from)
    if date_to:
        scan_logs = scan_logs.filter(scan_datetime__date__lte=date_to)
    
    # Apply time filters
    if time_from:
        scan_logs = scan_logs.filter(scan_datetime__time__gte=time_from)
    if time_to:
        scan_logs = scan_logs.filter(scan_datetime__time__lte=time_to)
    
    # Apply sorting
    sort_options = {
        'scan_datetime': '-scan_datetime',
        'scan_datetime_asc': 'scan_datetime',
        'user': 'scanned_by__last_name',
        'item': 'supply_item__name',
        'location': 'location',
        'scan_type': 'scan_type'
    }
    scan_logs = scan_logs.order_by(sort_options.get(sort_by, '-scan_datetime'))
    
    # Pagination
    paginator = Paginator(scan_logs, 25)
    page_obj = paginator.get_page(page)
    
    # Get filter options
    users = UserProfile.objects.select_related('user').all()
    items = SupplyItem.objects.all().order_by('name')
    locations = QRScanLog.objects.values_list('location', flat=True).distinct().order_by('location')
    
    context = {
        'page_obj': page_obj,
        'users': users,
        'items': items,
        'locations': locations,
        'search_params': {
            'q': query,
            'scan_type': scan_type,
            'user': user_id,
            'item': item_id,
            'location': location,
            'date_from': date_from,
            'date_to': date_to,
            'time_from': time_from,
            'time_to': time_to,
            'sort': sort_by,
        },
        'scan_type_choices': QRScanLog.SCAN_TYPE_CHOICES,
        'total_results': paginator.count,
    }
    
    # Return partial template for HTMX requests
    if request.headers.get('HX-Request'):
        return render(request, 'search/partials/scan_log_results.html', context)
    
    return render(request, 'search/advanced_scan_log_search.html', context)


@login_required
def autocomplete_suggestions(request):
    """HTMX endpoint for autocomplete suggestions"""
    query = request.GET.get('q', '').strip()
    search_type = request.GET.get('type', 'all')  # 'items', 'requests', 'users', 'all'
    
    if len(query) < 2:
        return HttpResponse('')
    
    suggestions = []
    
    if search_type in ['items', 'all']:
        # Supply item suggestions
        items = SupplyItem.objects.filter(
            Q(name__icontains=query) |
            Q(description__icontains=query)
        ).select_related('category')[:5]
        
        for item in items:
            suggestions.append({
                'type': 'item',
                'id': item.id,
                'text': item.name,
                'subtitle': f"{item.category.name} • {item.current_stock} {item.unit_of_measure}",
                'url': f'/inventory/{item.id}/',
                'icon': 'cube'
            })
    
    if search_type in ['requests', 'all'] and request.user.userprofile.role in ['admin', 'gso_staff']:
        # Request suggestions
        requests = SupplyRequest.objects.filter(
            Q(request_number__icontains=query) |
            Q(department__icontains=query)
        ).select_related('requester')[:3]
        
        for req in requests:
            suggestions.append({
                'type': 'request',
                'id': req.id,
                'text': req.request_number,
                'subtitle': f"{req.department} • {req.get_status_display()}",
                'url': f'/requests/{req.id}/',
                'icon': 'document-text'
            })
    
    if search_type in ['users', 'all'] and request.user.userprofile.role in ['admin', 'gso_staff']:
        # User suggestions
        users = UserProfile.objects.filter(
            Q(user__username__icontains=query) |
            Q(user__first_name__icontains=query) |
            Q(user__last_name__icontains=query)
        ).select_related('user')[:3]
        
        for user_profile in users:
            suggestions.append({
                'type': 'user',
                'id': user_profile.user.id,
                'text': user_profile.user.get_full_name() or user_profile.user.username,
                'subtitle': f"{user_profile.department} • {user_profile.get_role_display()}",
                'url': f'/users/{user_profile.user.id}/',
                'icon': 'user'
            })
    
    return render(request, 'search/autocomplete_suggestions.html', {
        'suggestions': suggestions,
        'query': query
    })


@login_required
def save_search(request):
    """Save a search query for quick access"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            name = data.get('name', '').strip()
            search_type = data.get('search_type', '')
            search_params = data.get('search_params', {})
            is_public = data.get('is_public', False)

            if not name or not search_type:
                return JsonResponse({'error': 'Name and search type are required'}, status=400)

            # Check if user already has a saved search with this name and type
            existing = SavedSearch.objects.filter(
                user=request.user,
                name=name,
                search_type=search_type
            ).first()

            if existing:
                # Update existing search
                existing.search_params = json.dumps(search_params)
                existing.is_public = is_public
                existing.save()
                saved_search = existing
            else:
                # Create new saved search
                saved_search = SavedSearch.objects.create(
                    user=request.user,
                    name=name,
                    search_type=search_type,
                    search_params=json.dumps(search_params),
                    is_public=is_public
                )

            return JsonResponse({
                'success': True,
                'id': saved_search.id,
                'message': 'Search saved successfully'
            })

        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
def load_saved_search(request, search_id):
    """Load a saved search"""
    try:
        saved_search = get_object_or_404(
            SavedSearch,
            Q(user=request.user) | Q(is_public=True),
            id=search_id
        )

        # Increment usage count
        saved_search.increment_usage()

        # Build URL with search parameters
        search_params = json.loads(saved_search.search_params)
        url_params = []
        for key, value in search_params.items():
            if value:
                url_params.append(f"{key}={value}")

        # Determine the appropriate search URL
        search_urls = {
            'inventory': '/search/inventory/',
            'requests': '/search/requests/',
            'scan_logs': '/search/scan-logs/',
            'users': '/search/users/'
        }

        base_url = search_urls.get(saved_search.search_type, '/search/inventory/')
        full_url = f"{base_url}?{'&'.join(url_params)}" if url_params else base_url

        return JsonResponse({
            'success': True,
            'url': full_url,
            'search_type': saved_search.search_type,
            'name': saved_search.name
        })

    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
def delete_saved_search(request, search_id):
    """Delete a saved search"""
    if request.method == 'DELETE':
        try:
            saved_search = get_object_or_404(
                SavedSearch,
                user=request.user,
                id=search_id
            )

            saved_search.delete()

            return JsonResponse({
                'success': True,
                'message': 'Search deleted successfully'
            })

        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)

    return JsonResponse({'error': 'Method not allowed'}, status=405)


@login_required
def my_saved_searches(request):
    """List user's saved searches"""
    search_type = request.GET.get('type', '')

    saved_searches = SavedSearch.objects.filter(
        Q(user=request.user) | Q(is_public=True)
    )

    if search_type:
        saved_searches = saved_searches.filter(search_type=search_type)

    saved_searches = saved_searches.order_by('-last_used', '-created_at')

    # Group by search type
    grouped_searches = {}
    for search in saved_searches:
        search_type_key = search.search_type
        if search_type_key not in grouped_searches:
            grouped_searches[search_type_key] = []
        grouped_searches[search_type_key].append(search)

    context = {
        'grouped_searches': grouped_searches,
        'search_type_filter': search_type,
        'search_type_choices': SavedSearch.SEARCH_TYPE_CHOICES,
    }

    if request.headers.get('HX-Request'):
        return render(request, 'search/partials/saved_searches_list.html', context)

    return render(request, 'search/my_saved_searches.html', context)
