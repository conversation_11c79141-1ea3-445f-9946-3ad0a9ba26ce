from django.urls import path, include
from .views import home
from .auth_views import (
    custom_login, custom_register, custom_logout, 
    dashboard, admin_dashboard, gso_dashboard,
    dashboard_stats_htmx, dashboard_activities_htmx, dashboard_widgets_htmx
)
from .request_views import (
    create_request, request_list, request_detail, edit_request, delete_request,
    request_status_update_htmx, my_requests_htmx
)
from .approval_views import (
    pending_requests_view, approve_request_htmx, reject_request_htmx,
    bulk_approve_requests_htmx, rejection_modal_htmx, request_detail_modal_htmx,
    pending_requests_table_htmx, approval_history_view
)
from .qr_views import (
    qr_scanner, process_qr_scan, scan_history, get_supply_item_info,
    pending_requests_for_scan, qr_code_management, generate_qr_code,
    regenerate_qr_code, download_qr_code, batch_generate_qr_codes,
    qr_code_list, qr_code_preview
)
from .inventory_views import (
    inventory_list, inventory_list_htmx, add_supply_item, edit_supply_item,
    supply_item_detail, delete_supply_item, delete_confirmation_modal,
    stock_adjustment, stock_adjustment_list, low_stock_alerts, inventory_transactions
)
from .reports_views import (
    reports_dashboard, request_summary_report, inventory_report,
    usage_log_report, analytics_dashboard, export_report
)
from .htmx_views import (
    inline_edit_supply_item, inline_edit_request_item, quick_stock_adjustment,
    live_notifications, status_indicator, toggle_favorite_item, search_suggestions
)
from .search_views import (
    advanced_inventory_search, advanced_request_search, advanced_scan_log_search,
    autocomplete_suggestions, save_search, load_saved_search, delete_saved_search,
    my_saved_searches
)
from .notification_views import (
    notification_center, mark_notification_read, dismiss_notification,
    mark_all_read, dismiss_all_read, notification_preferences,
    live_notification_count, notification_dropdown, create_system_notification
)
from .gso_views import (
    gso_dashboard_main, gso_inventory_overview, gso_approvals_overview,
    gso_requests_overview, gso_qr_overview, gso_reports_overview, gso_quick_actions
)
from .admin_views import (
    admin_users_list, admin_users_add, admin_roles_management, admin_permissions,
    admin_settings, admin_settings_general, admin_settings_email, admin_settings_backup,
    admin_settings_security, admin_inventory, admin_requests, admin_bulk_operations,
    admin_performance_reports, admin_audit_logs, admin_notifications,
    admin_notifications_list, create_system_notification, admin_notification_templates,
    admin_notification_settings, admin_system_health, admin_users_count,
    admin_inventory_alerts_count, admin_pending_requests_count, admin_audit_alerts_count,
    admin_notification_alerts_count, admin_system_health_indicator, admin_quick_stats_htmx
)

urlpatterns = [
    path('', home, name='home'),

    # Authentication URLs
    path('login/', custom_login, name='login'),
    path('register/', custom_register, name='register'),
    path('logout/', custom_logout, name='logout'),

    # Dashboard URLs
    path('dashboard/', dashboard, name='dashboard'),
    path('admin-dashboard/', admin_dashboard, name='admin_dashboard'),
    path('gso-dashboard/', gso_dashboard, name='gso_dashboard'),

    # GSO-specific URLs with proper namespacing
    path('gso/', include([
        path('dashboard/', gso_dashboard_main, name='gso_dashboard_main'),

        # Inventory section
        path('inventory/', gso_inventory_overview, name='gso_inventory'),
        path('inventory/list/', inventory_list, name='gso_inventory_list'),
        path('inventory/add/', add_supply_item, name='gso_inventory_add'),
        path('inventory/<int:pk>/', supply_item_detail, name='gso_inventory_detail'),
        path('inventory/<int:pk>/edit/', edit_supply_item, name='gso_inventory_edit'),
        path('inventory/<int:pk>/delete/', delete_supply_item, name='gso_inventory_delete'),
        path('inventory/stock-adjustment/', stock_adjustment_list, name='gso_stock_adjustment'),
        path('inventory/<int:pk>/stock-adjustment/', stock_adjustment, name='gso_stock_adjustment_item'),
        path('inventory/low-stock/', low_stock_alerts, name='gso_low_stock'),
        path('inventory/transactions/', inventory_transactions, name='gso_inventory_transactions'),

        # Approvals section
        path('approvals/', gso_approvals_overview, name='gso_approvals'),
        path('approvals/pending/', pending_requests_view, name='gso_approvals_pending'),
        path('approvals/history/', approval_history_view, name='gso_approval_history'),

        # Requests section
        path('requests/', gso_requests_overview, name='gso_requests'),
        path('requests/<int:pk>/', request_detail, name='gso_request_detail'),

        # QR Scanner section
        path('qr-scanner/', gso_qr_overview, name='gso_qr_scanner'),
        path('qr-scanner/scan/', qr_scanner, name='gso_qr_scanner_tool'),
        path('qr-scanner/history/', scan_history, name='gso_scan_history'),
        path('qr-codes/', qr_code_management, name='gso_qr_management'),
        path('qr-codes/list/', qr_code_list, name='gso_qr_list'),

        # Reports section
        path('reports/', gso_reports_overview, name='gso_reports'),
        path('reports/inventory/', inventory_report, name='gso_inventory_report'),
        path('reports/requests/', request_summary_report, name='gso_request_report'),
        path('reports/usage/', usage_log_report, name='gso_usage_report'),
        path('reports/analytics/', analytics_dashboard, name='gso_analytics'),

        # HTMX endpoints
        path('htmx/quick-actions/', gso_quick_actions, name='gso_quick_actions_htmx'),
    ])),
    
    # Supply Request URLs
    path('requests/', request_list, name='request_list'),
    path('requests/create/', create_request, name='create_request'),
    path('requests/<int:pk>/', request_detail, name='request_detail'),
    path('requests/<int:pk>/edit/', edit_request, name='edit_request'),
    path('requests/<int:pk>/delete/', delete_request, name='delete_request'),
    
    # Approval URLs
    path('approvals/', pending_requests_view, name='pending_requests_view'),
    path('approvals/history/', approval_history_view, name='approval_history_view'),
    
    # HTMX endpoints for real-time updates
    path('htmx/dashboard-stats/', dashboard_stats_htmx, name='dashboard_stats_htmx'),
    path('htmx/dashboard-activities/', dashboard_activities_htmx, name='dashboard_activities_htmx'),
    path('htmx/dashboard-widgets/', dashboard_widgets_htmx, name='dashboard_widgets_htmx'),
    path('htmx/request-status/<int:pk>/', request_status_update_htmx, name='request_status_update_htmx'),
    path('htmx/my-requests/', my_requests_htmx, name='my_requests_htmx'),
    
    # Approval HTMX endpoints
    path('htmx/approve-request/<int:request_id>/', approve_request_htmx, name='approve_request_htmx'),
    path('htmx/reject-request/<int:request_id>/', reject_request_htmx, name='reject_request_htmx'),
    path('htmx/bulk-approve/', bulk_approve_requests_htmx, name='bulk_approve_requests_htmx'),
    path('htmx/rejection-modal/<int:request_id>/', rejection_modal_htmx, name='rejection_modal_htmx'),
    path('htmx/request-detail-modal/<int:request_id>/', request_detail_modal_htmx, name='request_detail_modal_htmx'),
    path('htmx/pending-requests-table/', pending_requests_table_htmx, name='pending_requests_table_htmx'),
    
    # QR Code Scanner URLs
    path('qr-scanner/', qr_scanner, name='qr_scanner'),
    path('scan-history/', scan_history, name='scan_history'),
    path('api/process-qr-scan/', process_qr_scan, name='process_qr_scan'),
    path('api/supply-item/<str:qr_code_data>/', get_supply_item_info, name='get_supply_item_info'),
    path('api/pending-requests-for-scan/', pending_requests_for_scan, name='pending_requests_for_scan'),
    
    # QR Code Management URLs
    path('qr-management/', qr_code_management, name='qr_code_management'),
    path('qr-codes/', qr_code_list, name='qr_code_list'),
    path('qr-codes/generate/<int:item_id>/', generate_qr_code, name='generate_qr_code'),
    path('qr-codes/regenerate/<int:item_id>/', regenerate_qr_code, name='regenerate_qr_code'),
    path('qr-codes/download/<int:item_id>/', download_qr_code, name='download_qr_code'),
    path('qr-codes/preview/<int:item_id>/', qr_code_preview, name='qr_code_preview'),
    path('api/batch-generate-qr-codes/', batch_generate_qr_codes, name='batch_generate_qr_codes'),
    
    # Inventory Management URLs
    path('inventory/', inventory_list, name='inventory_list'),
    path('inventory/add/', add_supply_item, name='add_supply_item'),
    path('inventory/<int:pk>/', supply_item_detail, name='supply_item_detail'),
    path('inventory/<int:pk>/edit/', edit_supply_item, name='edit_supply_item'),
    path('inventory/<int:pk>/delete/', delete_supply_item, name='delete_supply_item'),
    path('inventory/<int:pk>/adjust-stock/', stock_adjustment, name='stock_adjustment'),
    path('inventory/low-stock/', low_stock_alerts, name='low_stock_alerts'),
    path('inventory/transactions/', inventory_transactions, name='inventory_transactions'),
    
    # Inventory HTMX endpoints
    path('htmx/inventory-list/', inventory_list_htmx, name='inventory_list_htmx'),
    path('htmx/delete-confirmation/<int:pk>/', delete_confirmation_modal, name='delete_confirmation_modal'),

    # Reports and Analytics
    path('reports/', reports_dashboard, name='reports_dashboard'),
    path('reports/request-summary/', request_summary_report, name='request_summary_report'),
    path('reports/inventory/', inventory_report, name='inventory_report'),
    path('reports/usage-log/', usage_log_report, name='usage_log_report'),
    path('reports/analytics/', analytics_dashboard, name='analytics_dashboard'),
    path('reports/export/<str:report_type>/', export_report, name='export_report'),

    # Admin Panel URLs (using 'admin-panel/' to avoid conflict with Django admin)
    path('admin-panel/', include([
        # User Management
        path('users/', admin_users_list, name='admin_users'),
        path('users/list/', admin_users_list, name='admin_users_list'),
        path('users/add/', admin_users_add, name='admin_users_add'),
        path('users/roles/', admin_roles_management, name='admin_roles_management'),
        path('users/permissions/', admin_permissions, name='admin_permissions'),

        # System Settings
        path('settings/', admin_settings, name='admin_settings'),
        path('settings/general/', admin_settings_general, name='admin_settings_general'),
        path('settings/email/', admin_settings_email, name='admin_settings_email'),
        path('settings/backup/', admin_settings_backup, name='admin_settings_backup'),
        path('settings/security/', admin_settings_security, name='admin_settings_security'),

        # Admin Inventory
        path('inventory/', admin_inventory, name='admin_inventory'),

        # Admin Requests
        path('requests/', admin_requests, name='admin_requests'),
        path('requests/bulk-operations/', admin_bulk_operations, name='admin_bulk_operations'),

        # Admin Reports
        path('reports/performance/', admin_performance_reports, name='admin_performance_reports'),

        # Audit Logs
        path('audit-logs/', admin_audit_logs, name='admin_audit_logs'),

        # Notifications Management
        path('notifications/', admin_notifications, name='admin_notifications'),
        path('notifications/list/', admin_notifications_list, name='admin_notifications_list'),
        path('notifications/create/', create_system_notification, name='create_system_notification'),
        path('notifications/templates/', admin_notification_templates, name='admin_notification_templates'),
        path('notifications/settings/', admin_notification_settings, name='admin_notification_settings'),

        # System Health
        path('system-health/', admin_system_health, name='admin_system_health'),

        # HTMX endpoints for badges and indicators
        path('htmx/users-count/', admin_users_count, name='admin_users_count'),
        path('htmx/inventory-alerts-count/', admin_inventory_alerts_count, name='admin_inventory_alerts_count'),
        path('htmx/pending-requests-count/', admin_pending_requests_count, name='admin_pending_requests_count'),
        path('htmx/audit-alerts-count/', admin_audit_alerts_count, name='admin_audit_alerts_count'),
        path('htmx/notification-alerts-count/', admin_notification_alerts_count, name='admin_notification_alerts_count'),
        path('htmx/system-health-indicator/', admin_system_health_indicator, name='admin_system_health_indicator'),
        path('htmx/quick-stats/', admin_quick_stats_htmx, name='admin_quick_stats_htmx'),
    ])),

    # Advanced HTMX endpoints
    path('htmx/inline-edit-item/<int:item_id>/', inline_edit_supply_item, name='inline_edit_supply_item'),
    path('htmx/inline-edit-request-item/<int:item_id>/', inline_edit_request_item, name='inline_edit_request_item'),
    path('htmx/quick-stock-adjustment/<int:item_id>/', quick_stock_adjustment, name='quick_stock_adjustment'),
    path('htmx/live-notifications/', live_notifications, name='live_notifications'),
    path('htmx/status-indicator/<str:model_type>/<int:object_id>/', status_indicator, name='status_indicator'),
    path('htmx/toggle-favorite/<int:item_id>/', toggle_favorite_item, name='toggle_favorite_item'),
    path('htmx/search-suggestions/', search_suggestions, name='search_suggestions'),

    # Advanced Search and Filtering
    path('search/inventory/', advanced_inventory_search, name='advanced_inventory_search'),
    path('search/requests/', advanced_request_search, name='advanced_request_search'),
    path('search/scan-logs/', advanced_scan_log_search, name='advanced_scan_log_search'),
    path('search/autocomplete/', autocomplete_suggestions, name='autocomplete_suggestions'),
    path('search/save/', save_search, name='save_search'),
    path('search/load/<int:search_id>/', load_saved_search, name='load_saved_search'),
    path('search/delete/<int:search_id>/', delete_saved_search, name='delete_saved_search'),
    path('search/my-searches/', my_saved_searches, name='my_saved_searches'),

    # Notification URLs
    path('notifications/', notification_center, name='notification_center'),
    path('notifications/read/<int:notification_id>/', mark_notification_read, name='mark_notification_read'),
    path('notifications/dismiss/<int:notification_id>/', dismiss_notification, name='dismiss_notification'),
    path('notifications/mark-all-read/', mark_all_read, name='mark_all_read'),
    path('notifications/dismiss-all-read/', dismiss_all_read, name='dismiss_all_read'),
    path('notifications/preferences/', notification_preferences, name='notification_preferences'),
    path('notifications/live-count/', live_notification_count, name='live_notification_count'),
    path('notifications/dropdown/', notification_dropdown, name='notification_dropdown'),
    path('notifications/create-system/', create_system_notification, name='create_system_notification'),
]
