{% extends 'base.html' %}
{% load static %}

{% block title %}Access Denied - Smart Supply Management{% endblock %}

{% block page_title %}Access Denied{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <!-- Error Icon -->
            <div class="mx-auto h-24 w-24 text-red-500 mb-6">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            
            <!-- Error Message -->
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Access Denied
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                You don't have permission to access this resource.
            </p>
            
            <!-- Additional Info -->
            <div class="mt-6 bg-red-50 border border-red-200 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">
                            Insufficient Privileges
                        </h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>This page requires specific user permissions that your account doesn't have. Please contact your administrator if you believe this is an error.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Action Buttons -->
            <div class="mt-8 space-y-4">
                {% if user.is_authenticated %}
                    <a href="{% url 'dashboard' %}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Return to Dashboard
                    </a>
                    
                    {% if user.userprofile.role == 'gso_staff' %}
                    <a href="{% url 'gso_dashboard_main' %}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Go to GSO Dashboard
                    </a>
                    {% endif %}
                {% else %}
                    <a href="{% url 'login' %}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        Sign In
                    </a>
                {% endif %}
                
                <button onclick="history.back()" 
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    Go Back
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-redirect after 30 seconds if user is authenticated
    {% if user.is_authenticated %}
    setTimeout(function() {
        {% if user.userprofile.role == 'gso_staff' %}
            window.location.href = '{% url "gso_dashboard_main" %}';
        {% else %}
            window.location.href = '{% url "dashboard" %}';
        {% endif %}
    }, 30000);
    {% endif %}
</script>
{% endblock %}
