/**
 * GSO Navigation JavaScript Module
 * Handles sidebar navigation, submenu toggles, active states, and user interactions
 * for GSO (Government Services Office) users.
 */

class GSONavigation {
    constructor() {
        this.navLinks = null;
        this.navSections = null;
        this.currentPath = window.location.pathname;
        this.storagePrefix = 'gso-nav-';
        
        this.init();
    }
    
    /**
     * Initialize GSO navigation functionality
     */
    init() {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }
    
    /**
     * Setup navigation functionality
     */
    setup() {
        this.cacheElements();
        this.setActiveNavItem();
        this.initSubmenuToggles();
        this.initNavStatePersistence();
        this.initKeyboardShortcuts();
        this.initMobileNavigation();
        
        // Debug logging
        console.log('GSO Navigation initialized for path:', this.currentPath);
    }
    
    /**
     * Cache DOM elements for better performance
     */
    cacheElements() {
        this.navLinks = document.querySelectorAll('.nav-link, .nav-sublink');
        this.navSections = document.querySelectorAll('.nav-section');
    }
    
    /**
     * Set active navigation item based on current URL
     */
    setActiveNavItem() {
        if (!this.navLinks) return;
        
        // Remove all active states first
        this.navLinks.forEach(link => {
            this.removeActiveClasses(link);
        });
        
        let activeLink = null;
        let activeSection = null;
        
        // Find the best matching link (longest matching path)
        this.navLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && this.isPathMatch(href)) {
                if (!activeLink || href.length > activeLink.getAttribute('href').length) {
                    activeLink = link;
                    activeSection = link.closest('.nav-section');
                }
            }
        });
        
        // Apply active state to the best match
        if (activeLink) {
            this.setActiveClasses(activeLink, activeSection);
        }
    }
    
    /**
     * Check if current path matches the given href
     */
    isPathMatch(href) {
        // Exact match first
        if (this.currentPath === href) return true;

        // For GSO paths, check if current path starts with href (but not just '/gso/')
        if (href !== '/gso/' && this.currentPath.startsWith(href)) return true;

        // Handle special cases for nested routes
        if (href.includes('/gso/') && this.currentPath.includes('/gso/')) {
            // Extract the base path for comparison
            const hrefBase = href.split('?')[0].split('#')[0];
            const currentBase = this.currentPath.split('?')[0].split('#')[0];

            // Check if current path is a sub-path of the href
            if (currentBase.startsWith(hrefBase)) return true;
        }

        return false;
    }
    
    /**
     * Remove active classes from a navigation link
     */
    removeActiveClasses(link) {
        link.classList.remove('active', 'bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
        link.classList.add('text-gray-700');
    }
    
    /**
     * Set active classes on a navigation link
     */
    setActiveClasses(activeLink, activeSection) {
        activeLink.classList.remove('text-gray-700');
        activeLink.classList.add('active', 'bg-blue-100', 'text-blue-700', 'border-r-2', 'border-blue-500');
        
        // Handle submenu parent highlighting
        if (activeLink.classList.contains('nav-sublink') && activeSection) {
            const parentLink = activeSection.querySelector('.nav-link');
            if (parentLink) {
                parentLink.classList.remove('text-gray-700');
                parentLink.classList.add('bg-blue-50', 'text-blue-600');
                
                // Expand the submenu
                this.expandSubmenu(activeSection);
            }
        }
    }
    
    /**
     * Expand a submenu section
     */
    expandSubmenu(section) {
        const submenu = section.querySelector('.nav-submenu');
        const arrow = section.querySelector('.nav-link svg:last-child');
        
        if (submenu) {
            submenu.classList.remove('hidden');
            if (arrow) {
                arrow.classList.add('rotate-180');
            }
        }
    }
    
    /**
     * Initialize submenu toggle functionality
     */
    initSubmenuToggles() {
        if (!this.navSections) return;

        this.navSections.forEach(section => {
            const mainLink = section.querySelector('.nav-link');
            const submenu = section.querySelector('.nav-submenu');

            if (mainLink && submenu) {
                // Add the click event listener to the main link
                mainLink.addEventListener('click', (e) => {
                    // Get the arrow element fresh each time
                    const arrow = mainLink.querySelector('svg:last-child');
                    this.handleSubmenuToggle(e, section, submenu, arrow);
                });
            }
        });
    }
    
    /**
     * Handle submenu toggle click
     */
    handleSubmenuToggle(event, section, submenu, arrow) {
        const clickedElement = event.target;
        const mainLink = section.querySelector('.nav-link');
        const mainLinkHref = mainLink ? mainLink.getAttribute('href') : null;
        const hasSubmenuItems = submenu.querySelectorAll('.nav-sublink').length > 0;

        // Check if click was on arrow (SVG element or its children)
        const isArrowClick = clickedElement.closest('svg:last-child') ||
                           clickedElement.tagName === 'svg' ||
                           clickedElement.tagName === 'path';

        // If there are submenu items, we should handle the toggle
        if (hasSubmenuItems) {
            // If clicking the arrow, always toggle
            // If clicking the main link and it has no valid href, also toggle
            if (isArrowClick || (!mainLinkHref || mainLinkHref === '#')) {
                event.preventDefault();

                // Toggle submenu visibility
                const isCurrentlyHidden = submenu.classList.contains('hidden');

                if (isCurrentlyHidden) {
                    submenu.classList.remove('hidden');
                } else {
                    submenu.classList.add('hidden');
                }

                // Toggle arrow rotation
                if (arrow) {
                    if (isCurrentlyHidden) {
                        arrow.classList.add('rotate-180');
                    } else {
                        arrow.classList.remove('rotate-180');
                    }
                }

                // Save state to localStorage
                this.saveSubmenuState(section, !isCurrentlyHidden);

                console.log('Submenu toggled:', {
                    section: section.getAttribute('data-nav-section'),
                    isNowOpen: !isCurrentlyHidden,
                    hasItems: hasSubmenuItems
                });

                return;
            }
        }

        // If clicking the main link and it has a valid href, allow navigation
        if (mainLinkHref && mainLinkHref !== '#') {
            // Allow the default navigation behavior
            return;
        }
    }
    
    /**
     * Save submenu state to localStorage
     */
    saveSubmenuState(section, isOpen) {
        const sectionName = section.getAttribute('data-nav-section');
        if (sectionName) {
            localStorage.setItem(`${this.storagePrefix}${sectionName}`, isOpen.toString());
        }
    }
    
    /**
     * Initialize navigation state persistence
     */
    initNavStatePersistence() {
        if (!this.navSections) return;
        
        this.navSections.forEach(section => {
            const sectionName = section.getAttribute('data-nav-section');
            if (sectionName) {
                const isOpen = localStorage.getItem(`${this.storagePrefix}${sectionName}`) === 'true';
                if (isOpen) {
                    this.expandSubmenu(section);
                }
            }
        });
    }
    
    /**
     * Initialize keyboard shortcuts for GSO users
     */
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Only handle shortcuts if Alt key is pressed and no input is focused
            if (!e.altKey || this.isInputFocused()) return;
            
            switch (e.key.toLowerCase()) {
                case 'd':
                    e.preventDefault();
                    this.navigateToUrl('gso_dashboard_main');
                    break;
                case 'i':
                    e.preventDefault();
                    this.navigateToUrl('gso_inventory');
                    break;
                case 'a':
                    e.preventDefault();
                    this.navigateToUrl('gso_approvals');
                    break;
                case 'r':
                    e.preventDefault();
                    this.navigateToUrl('gso_requests');
                    break;
                case 'q':
                    e.preventDefault();
                    this.navigateToUrl('gso_qr_scanner');
                    break;
            }
        });
    }
    
    /**
     * Check if an input element is currently focused
     */
    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.isContentEditable
        );
    }
    
    /**
     * Navigate to a URL by name using Django URL resolution
     */
    navigateToUrl(urlName) {
        // Try to find the URL from existing navigation links first
        const navLink = document.querySelector(`[data-nav-item="${urlName}"]`);
        if (navLink && navLink.href) {
            window.location.href = navLink.href;
            return;
        }

        // Fallback to URL mapping for keyboard shortcuts
        const urlMap = {
            'dashboard': '/gso/dashboard/',
            'inventory': '/gso/inventory/',
            'approvals': '/gso/approvals/',
            'requests': '/gso/requests/',
            'qr-scanner': '/gso/qr-scanner/',
            'reports': '/gso/reports/'
        };

        const url = urlMap[urlName];
        if (url) {
            window.location.href = url;
        } else {
            console.warn(`No URL found for navigation item: ${urlName}`);
        }
    }
    
    /**
     * Initialize mobile navigation enhancements
     */
    initMobileNavigation() {
        // Add touch-friendly interactions for mobile devices
        if ('ontouchstart' in window) {
            this.navLinks.forEach(link => {
                link.addEventListener('touchstart', () => {
                    // Add visual feedback for touch
                    link.style.backgroundColor = 'rgba(59, 130, 246, 0.1)';
                });
                
                link.addEventListener('touchend', () => {
                    // Remove visual feedback
                    setTimeout(() => {
                        link.style.backgroundColor = '';
                    }, 150);
                });
            });
        }
    }
    
    /**
     * Refresh navigation state (useful for dynamic content)
     */
    refresh() {
        this.currentPath = window.location.pathname;
        this.cacheElements();
        this.setActiveNavItem();
    }
    
    /**
     * Get current navigation state
     */
    getState() {
        const state = {
            currentPath: this.currentPath,
            activeLinks: [],
            openSubmenus: [],
            breadcrumbs: this.generateBreadcrumbs()
        };

        // Get active links
        document.querySelectorAll('.nav-link.active, .nav-sublink.active').forEach(link => {
            state.activeLinks.push({
                href: link.getAttribute('href'),
                text: link.textContent.trim(),
                isSublink: link.classList.contains('nav-sublink')
            });
        });

        // Get open submenus
        document.querySelectorAll('.nav-submenu:not(.hidden)').forEach(submenu => {
            const section = submenu.closest('.nav-section');
            const sectionName = section?.getAttribute('data-nav-section');
            if (sectionName) {
                state.openSubmenus.push(sectionName);
            }
        });

        return state;
    }

    /**
     * Generate breadcrumbs based on current path
     */
    generateBreadcrumbs() {
        const breadcrumbs = [
            { name: 'GSO', url: '/gso/dashboard/' }
        ];

        const pathSegments = this.currentPath.split('/').filter(segment => segment);

        if (pathSegments.length > 1) {
            const section = pathSegments[1];

            switch (section) {
                case 'dashboard':
                    breadcrumbs.push({ name: 'Dashboard', url: null });
                    break;
                case 'inventory':
                    breadcrumbs.push({ name: 'Inventory', url: '/gso/inventory/' });
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        if (subsection === 'add') {
                            breadcrumbs.push({ name: 'Add Item', url: null });
                        } else if (subsection === 'low-stock') {
                            breadcrumbs.push({ name: 'Low Stock Alerts', url: null });
                        } else if (subsection === 'transactions') {
                            breadcrumbs.push({ name: 'Transactions', url: null });
                        } else if (subsection === 'stock-adjustment') {
                            breadcrumbs.push({ name: 'Stock Adjustment', url: null });
                        }
                    }
                    break;
                case 'approvals':
                    breadcrumbs.push({ name: 'Approvals', url: '/gso/approvals/' });
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        if (subsection === 'pending') {
                            breadcrumbs.push({ name: 'Pending Requests', url: null });
                        } else if (subsection === 'history') {
                            breadcrumbs.push({ name: 'History', url: null });
                        }
                    }
                    break;
                case 'requests':
                    breadcrumbs.push({ name: 'Requests', url: '/gso/requests/' });
                    break;
                case 'qr-scanner':
                    breadcrumbs.push({ name: 'QR Scanner', url: '/gso/qr-scanner/' });
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        if (subsection === 'scan') {
                            breadcrumbs.push({ name: 'Scan Tool', url: null });
                        } else if (subsection === 'history') {
                            breadcrumbs.push({ name: 'Scan History', url: null });
                        }
                    }
                    break;
                case 'qr-codes':
                    breadcrumbs.push({ name: 'QR Scanner', url: '/gso/qr-scanner/' });
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        if (subsection === 'list') {
                            breadcrumbs.push({ name: 'QR Code List', url: null });
                        } else {
                            breadcrumbs.push({ name: 'QR Management', url: null });
                        }
                    }
                    break;
                case 'reports':
                    breadcrumbs.push({ name: 'Reports', url: '/gso/reports/' });
                    if (pathSegments.length > 2) {
                        const subsection = pathSegments[2];
                        if (subsection === 'inventory') {
                            breadcrumbs.push({ name: 'Inventory Report', url: null });
                        } else if (subsection === 'requests') {
                            breadcrumbs.push({ name: 'Request Report', url: null });
                        } else if (subsection === 'usage') {
                            breadcrumbs.push({ name: 'Usage Report', url: null });
                        } else if (subsection === 'analytics') {
                            breadcrumbs.push({ name: 'Analytics', url: null });
                        }
                    }
                    break;
                default:
                    breadcrumbs.push({ name: 'Unknown', url: null });
            }
        }

        return breadcrumbs;
    }
}

// Global functions for backward compatibility
let gsoNavigation = null;

function initGSONavigation() {
    if (!gsoNavigation) {
        gsoNavigation = new GSONavigation();
    }
    return gsoNavigation;
}

function setActiveNavItem() {
    if (gsoNavigation) {
        gsoNavigation.setActiveNavItem();
    }
}

function initSubmenuToggles() {
    if (gsoNavigation) {
        gsoNavigation.initSubmenuToggles();
    }
}

function initNavStatePersistence() {
    if (gsoNavigation) {
        gsoNavigation.initNavStatePersistence();
    }
}

// Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('.nav-link[data-nav-item]')) {
        initGSONavigation();
    }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { GSONavigation, initGSONavigation };
}
