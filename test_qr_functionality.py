#!/usr/bin/env python
"""
Comprehensive test runner for QR code functionality
"""
import os
import sys
import django
from django.test.utils import get_runner
from django.conf import settings

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

def run_qr_tests():
    """Run all QR code related tests"""
    print("=" * 60)
    print("RUNNING QR CODE FUNCTIONALITY TESTS")
    print("=" * 60)
    
    # Get Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner(verbosity=2, interactive=False, keepdb=False)
    
    # Define test modules to run
    test_modules = [
        'suptrack.tests.test_qr_models',
        'suptrack.tests.test_qr_utils', 
        'suptrack.tests.test_qr_views',
    ]
    
    print(f"\nRunning tests for modules: {', '.join(test_modules)}")
    print("-" * 60)
    
    # Run the tests
    failures = test_runner.run_tests(test_modules)
    
    print("-" * 60)
    if failures:
        print(f"❌ TESTS FAILED: {failures} test(s) failed")
        return False
    else:
        print("✅ ALL QR CODE TESTS PASSED!")
        return True

def run_manual_qr_tests():
    """Run manual tests for QR code functionality"""
    print("\n" + "=" * 60)
    print("RUNNING MANUAL QR CODE TESTS")
    print("=" * 60)
    
    from django.contrib.auth.models import User
    from suptrack.models import UserProfile, SupplyCategory, SupplyItem, QRScanLog
    from suptrack.qr_utils import generate_qr_code_for_item, QRCodeGenerator
    
    try:
        # Test 1: QR Code Data Generation
        print("\n1. Testing QR code data generation...")
        
        category, created = SupplyCategory.objects.get_or_create(
            name='Test QR Category',
            defaults={'description': 'Category for QR testing'}
        )
        
        item = SupplyItem.objects.create(
            name='QR Test Item',
            category=category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        if item.qr_code_data:
            print(f"✅ QR code data auto-generated: {item.qr_code_data}")
        else:
            print("❌ QR code data not generated")
            return False
        
        # Test 2: QR Code Image Generation
        print("\n2. Testing QR code image generation...")
        
        generator = QRCodeGenerator()
        qr_image = generator.generate_qr_code_image(item, include_label=True)
        
        if qr_image:
            print(f"✅ QR code image generated: {qr_image.size}")
        else:
            print("❌ QR code image not generated")
            return False
        
        # Test 3: QR Code File Saving
        print("\n3. Testing QR code file saving...")
        
        try:
            qr_url = generate_qr_code_for_item(item, include_label=True)
            item.refresh_from_db()
            
            if item.qr_code and qr_url:
                print(f"✅ QR code file saved: {qr_url}")
            else:
                print("❌ QR code file not saved")
                return False
        except Exception as e:
            print(f"❌ Error saving QR code: {e}")
            return False
        
        # Test 4: QR Scan Log Creation
        print("\n4. Testing QR scan log creation...")
        
        user, created = User.objects.get_or_create(
            username='qr_test_user',
            defaults={'password': 'testpass123'}
        )
        
        profile, created = UserProfile.objects.get_or_create(
            user=user,
            defaults={'role': 'gso_staff', 'department': 'GSO'}
        )
        
        scan_log = QRScanLog.objects.create(
            supply_item=item,
            scanned_by=user,
            scan_type='inventory_check',
            location='Test Location',
            notes='Test scan'
        )
        
        if scan_log.id:
            print(f"✅ QR scan log created: ID {scan_log.id}")
        else:
            print("❌ QR scan log not created")
            return False
        
        # Test 5: QR Code Data Uniqueness
        print("\n5. Testing QR code data uniqueness...")
        
        item2 = SupplyItem.objects.create(
            name='QR Test Item 2',
            category=category,
            unit_of_measure='pieces',
            current_stock=5,
            minimum_stock=2
        )
        
        if item.qr_code_data != item2.qr_code_data:
            print(f"✅ QR code data is unique")
            print(f"   Item 1: {item.qr_code_data}")
            print(f"   Item 2: {item2.qr_code_data}")
        else:
            print("❌ QR code data is not unique")
            return False
        
        # Test 6: Batch QR Code Generation
        print("\n6. Testing batch QR code generation...")
        
        items = []
        for i in range(3):
            test_item = SupplyItem.objects.create(
                name=f'Batch Test Item {i+1}',
                category=category,
                unit_of_measure='pieces',
                current_stock=10,
                minimum_stock=5
            )
            items.append(test_item)
        
        generator = QRCodeGenerator()
        results = generator.batch_generate_qr_codes(items, include_label=True)
        
        success_count = sum(1 for result in results if result['success'])
        
        if success_count == len(items):
            print(f"✅ Batch QR code generation successful: {success_count}/{len(items)}")
        else:
            print(f"❌ Batch QR code generation failed: {success_count}/{len(items)}")
            return False
        
        print("\n" + "=" * 60)
        print("✅ ALL MANUAL QR CODE TESTS PASSED!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ Manual test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    print("Starting QR Code Functionality Tests...")
    
    # Run Django unit tests
    unit_tests_passed = run_qr_tests()
    
    # Run manual tests
    manual_tests_passed = run_manual_qr_tests()
    
    print("\n" + "=" * 60)
    print("FINAL TEST RESULTS")
    print("=" * 60)
    
    if unit_tests_passed and manual_tests_passed:
        print("🎉 ALL QR CODE TESTS PASSED SUCCESSFULLY!")
        print("\nQR Code functionality is working correctly:")
        print("✅ QR code data generation")
        print("✅ QR code image creation")
        print("✅ QR code file saving")
        print("✅ QR scan logging")
        print("✅ Batch operations")
        print("✅ View access control")
        print("✅ Scan processing")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED!")
        if not unit_tests_passed:
            print("- Unit tests failed")
        if not manual_tests_passed:
            print("- Manual tests failed")
        sys.exit(1)
