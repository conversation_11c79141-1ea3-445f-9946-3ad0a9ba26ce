{% extends 'base.html' %}
{% load static %}

{% block title %}Edit Request {{ request.request_number }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Edit Request {{ request.request_number }}</h1>
                    <p class="mt-1 text-sm text-gray-600">Modify your supply request</p>
                </div>
                <a href="{% url 'request_detail' request.pk %}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Back to Request
                </a>
            </div>
        </div>

        <!-- Messages -->
        {% if messages %}
            <div class="mb-6">
                {% for message in messages %}
                    <div class="bg-{{ message.tags == 'error' and 'red' or 'green' }}-100 border border-{{ message.tags == 'error' and 'red' or 'green' }}-400 text-{{ message.tags == 'error' and 'red' or 'green' }}-700 px-4 py-3 rounded mb-4">
                        {{ message }}
                    </div>
                {% endfor %}
            </div>
        {% endif %}

        <!-- Edit Form -->
        <div class="bg-white shadow rounded-lg">
            <form hx-post="{% url 'edit_request' request.pk %}" 
                  hx-target="#form-container" 
                  hx-swap="outerHTML"
                  class="space-y-6 p-6">
                {% csrf_token %}
                
                <div id="form-container">
                    <!-- Basic Request Information -->
                    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                        <div>
                            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Department *
                            </label>
                            <div class="mt-1">
                                {{ form.department }}
                                {% if form.department.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700">
                                Request Number
                            </label>
                            <div class="mt-1">
                                <input type="text" 
                                       value="{{ request.request_number }}" 
                                       readonly
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm">
                            </div>
                        </div>
                    </div>

                    <div>
                        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
                            Notes
                        </label>
                        <div class="mt-1">
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Request Items -->
                    <div class="border-t pt-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Request Items</h3>
                        
                        <div id="request-items">
                            {{ formset.management_form }}
                            {% for form in formset %}
                                <div class="request-item-form border border-gray-200 rounded-lg p-4 mb-4" data-form-index="{{ forloop.counter0 }}">
                                    {% for hidden in form.hidden_fields %}
                                        {{ hidden }}
                                    {% endfor %}
                                    
                                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                                        <div class="sm:col-span-2">
                                            <label for="{{ form.supply_item.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                                Supply Item *
                                            </label>
                                            <div class="mt-1">
                                                {{ form.supply_item }}
                                                {% if form.supply_item.errors %}
                                                    <p class="mt-1 text-sm text-red-600">{{ form.supply_item.errors.0 }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                        
                                        <div class="flex items-end">
                                            <div class="flex-1">
                                                <label for="{{ form.quantity_requested.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                                    Quantity *
                                                </label>
                                                <div class="mt-1">
                                                    {{ form.quantity_requested }}
                                                    {% if form.quantity_requested.errors %}
                                                        <p class="mt-1 text-sm text-red-600">{{ form.quantity_requested.errors.0 }}</p>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            
                                            <div class="ml-2 flex space-x-2">
                                                {% if form.DELETE %}
                                                    <label class="inline-flex items-center">
                                                        {{ form.DELETE }}
                                                        <span class="ml-1 text-sm text-red-600">Delete</span>
                                                    </label>
                                                {% endif %}
                                                
                                                <button type="button" 
                                                        class="inline-flex items-center p-2 border border-transparent rounded-md text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                                        onclick="removeRequestItem(this)">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <button type="button" 
                                id="add-item-btn"
                                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Another Item
                        </button>
                    </div>

                    <!-- Form Errors -->
                    {% if formset.non_form_errors %}
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            {{ formset.non_form_errors }}
                        </div>
                    {% endif %}

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-3 pt-6 border-t">
                        <a href="{% url 'request_detail' request.pk %}" 
                           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            Cancel
                        </a>
                        <button type="submit" 
                                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            Update Request
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let formIndex = {{ formset.total_form_count }};
    
    document.getElementById('add-item-btn').addEventListener('click', function() {
        const container = document.getElementById('request-items');
        const totalForms = document.getElementById('id_items-TOTAL_FORMS');
        
        // Create new form HTML
        const newFormHtml = `
            <div class="request-item-form border border-gray-200 rounded-lg p-4 mb-4" data-form-index="${formIndex}">
                <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                    <div class="sm:col-span-2">
                        <label for="id_items-${formIndex}-supply_item" class="block text-sm font-medium text-gray-700">
                            Supply Item *
                        </label>
                        <div class="mt-1">
                            <select name="items-${formIndex}-supply_item" id="id_items-${formIndex}-supply_item" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <option value="">Select a supply item</option>
                                {% for item in supply_items %}
                                    <option value="{{ item.pk }}">{{ item.name }} ({{ item.current_stock }} {{ item.unit_of_measure }} available)</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    
                    <div class="flex items-end">
                        <div class="flex-1">
                            <label for="id_items-${formIndex}-quantity_requested" class="block text-sm font-medium text-gray-700">
                                Quantity *
                            </label>
                            <div class="mt-1">
                                <input type="number" name="items-${formIndex}-quantity_requested" id="id_items-${formIndex}-quantity_requested" min="1" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Enter quantity">
                            </div>
                        </div>
                        
                        <button type="button" 
                                class="ml-2 inline-flex items-center p-2 border border-transparent rounded-md text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                onclick="removeRequestItem(this)">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Insert before the add button
        const addButton = document.getElementById('add-item-btn');
        addButton.insertAdjacentHTML('beforebegin', newFormHtml);
        
        // Update form count
        totalForms.value = parseInt(totalForms.value) + 1;
        formIndex++;
    });
});

function removeRequestItem(button) {
    const formDiv = button.closest('.request-item-form');
    const deleteCheckbox = formDiv.querySelector('input[name$="-DELETE"]');
    
    if (deleteCheckbox) {
        // Mark for deletion instead of removing
        deleteCheckbox.checked = true;
        formDiv.style.opacity = '0.5';
        formDiv.style.pointerEvents = 'none';
        
        // Add a visual indicator
        const indicator = document.createElement('div');
        indicator.className = 'absolute inset-0 bg-red-100 bg-opacity-75 flex items-center justify-center';
        indicator.innerHTML = '<span class="text-red-600 font-medium">Marked for deletion</span>';
        formDiv.style.position = 'relative';
        formDiv.appendChild(indicator);
    } else {
        // Remove new items that haven't been saved yet
        const totalForms = document.getElementById('id_items-TOTAL_FORMS');
        formDiv.remove();
        totalForms.value = parseInt(totalForms.value) - 1;
        
        // Reindex remaining forms
        const remainingForms = document.querySelectorAll('.request-item-form');
        remainingForms.forEach((form, index) => {
            form.setAttribute('data-form-index', index);
            
            // Update all form field names and IDs
            const inputs = form.querySelectorAll('input, select');
            inputs.forEach(input => {
                const name = input.getAttribute('name');
                const id = input.getAttribute('id');
                
                if (name && name.includes('items-')) {
                    const newName = name.replace(/items-\d+-/, `items-${index}-`);
                    input.setAttribute('name', newName);
                }
                
                if (id && id.includes('id_items-')) {
                    const newId = id.replace(/id_items-\d+-/, `id_items-${index}-`);
                    input.setAttribute('id', newId);
                }
            });
            
            // Update labels
            const labels = form.querySelectorAll('label');
            labels.forEach(label => {
                const forAttr = label.getAttribute('for');
                if (forAttr && forAttr.includes('id_items-')) {
                    const newFor = forAttr.replace(/id_items-\d+-/, `id_items-${index}-`);
                    label.setAttribute('for', newFor);
                }
            });
        });
    }
}
</script>
{% endblock %}