"""
Unit tests for GSO navigation functionality and sidebar behavior.
Tests the redirect mechanism, navigation state, and user experience.
"""

import json
from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from django.test.utils import override_settings
from unittest.mock import patch, MagicMock

from suptrack.models import UserProfile


class GSONavigationTestCase(TestCase):
    """Test GSO navigation functionality and redirect behavior"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        # Create test users
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            email='<EMAIL>',
            password='testpass123',
            first_name='GSO',
            last_name='Staff'
        )
        
        self.regular_user = User.objects.create_user(
            username='regular_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='Regular',
            last_name='User'
        )
        
        self.admin_user = User.objects.create_user(
            username='admin_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='Admin',
            last_name='User',
            is_staff=True
        )
        
        # Create user profiles
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='Government Services Office'
        )
        
        self.regular_profile = UserProfile.objects.create(
            user=self.regular_user,
            role='department_user',
            department='IT Department'
        )
        
        self.admin_profile = UserProfile.objects.create(
            user=self.admin_user,
            role='admin',
            department='Administration'
        )

    def test_gso_user_redirect_from_dashboard(self):
        """Test that GSO users are redirected from generic dashboard"""
        self.client.login(username='gso_staff', password='testpass123')

        # Access generic dashboard
        response = self.client.get(reverse('dashboard'))

        # Should be redirected to GSO dashboard
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('gso_dashboard_main'))

    def test_gso_user_redirect_from_home(self):
        """Test that GSO users are redirected from home page"""
        self.client.login(username='gso_staff', password='testpass123')

        # Access home page
        response = self.client.get('/')

        # Should be redirected to GSO dashboard
        self.assertEqual(response.status_code, 302)
        self.assertRedirects(response, reverse('gso_dashboard_main'))

    def test_gso_user_no_redirect_on_gso_pages(self):
        """Test that GSO users are not redirected when on GSO pages"""
        self.client.login(username='gso_staff', password='testpass123')
        
        # Access GSO dashboard directly
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should render normally without redirect script interference
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'GSO Dashboard')

    def test_regular_user_no_redirect(self):
        """Test that regular users don't get GSO redirect scripts"""
        self.client.login(username='regular_user', password='testpass123')
        
        # Access dashboard
        response = self.client.get(reverse('dashboard'))
        
        # Should not contain GSO redirect script
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'GSO User Auto-Redirect Logic')

    def test_gso_sidebar_navigation_included(self):
        """Test that GSO users see GSO-specific sidebar navigation"""
        self.client.login(username='gso_staff', password='testpass123')

        # Access any page that uses base template
        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include GSO sidebar navigation elements
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso/inventory/')
        self.assertContains(response, 'gso/approvals/')
        self.assertContains(response, 'data-nav-item="dashboard"')

    def test_regular_user_default_sidebar(self):
        """Test that regular users see default sidebar navigation"""
        self.client.login(username='regular_user', password='testpass123')
        
        # Access dashboard
        response = self.client.get(reverse('dashboard'))
        
        # Should not include GSO sidebar navigation
        self.assertEqual(response.status_code, 200)
        self.assertNotContains(response, 'gso_sidebar_nav.html')
        self.assertNotContains(response, 'gso_inventory')

    def test_gso_keyboard_shortcuts_included(self):
        """Test that GSO users get keyboard shortcuts"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include GSO navigation JavaScript
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')
        # The keyboard shortcuts are now handled in the separate JS file

    def test_gso_page_title_enhancement(self):
        """Test that GSO users get enhanced page titles"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should include GSO title enhancement script
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, "document.title = 'GSO - ' + document.title")

    def test_gso_navigation_urls_exist(self):
        """Test that all GSO navigation URLs are accessible"""
        self.client.login(username='gso_staff', password='testpass123')
        
        # Test main GSO URLs
        gso_urls = [
            'gso_dashboard_main',
            'gso_inventory',
            'gso_approvals',
            'gso_requests',
            'gso_qr_scanner',
            'gso_reports'
        ]
        
        for url_name in gso_urls:
            try:
                url = reverse(url_name)
                response = self.client.get(url)
                # Should be accessible (200) or redirect (302), not 404
                self.assertIn(response.status_code, [200, 302], 
                             f"URL {url_name} returned {response.status_code}")
            except Exception as e:
                self.fail(f"URL {url_name} failed: {str(e)}")

    def test_gso_submenu_urls_exist(self):
        """Test that GSO submenu URLs are accessible"""
        self.client.login(username='gso_staff', password='testpass123')

        # Test GSO submenu URLs (only those that don't require parameters)
        submenu_urls = [
            'gso_inventory_add',
            'gso_stock_adjustment',  # Now points to stock_adjustment_list
            'gso_low_stock',
            'gso_inventory_transactions',
            'gso_approvals_pending',
            'gso_approval_history',
            'gso_qr_scanner_tool',
            'gso_scan_history',
            'gso_qr_management',
            'gso_qr_list',
            'gso_inventory_report',
            'gso_request_report',
            'gso_usage_report',
            'gso_analytics'
        ]

        for url_name in submenu_urls:
            try:
                url = reverse(url_name)
                response = self.client.get(url)
                # Should be accessible (200) or redirect (302), not 404
                self.assertIn(response.status_code, [200, 302],
                             f"Submenu URL {url_name} returned {response.status_code}")
            except Exception as e:
                self.fail(f"Submenu URL {url_name} failed: {str(e)}")

    def test_gso_access_control(self):
        """Test that non-GSO users cannot access GSO pages"""
        self.client.login(username='regular_user', password='testpass123')
        
        # Try to access GSO dashboard
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should be redirected or denied access
        self.assertIn(response.status_code, [302, 403])

    def test_unauthenticated_user_no_gso_access(self):
        """Test that unauthenticated users cannot access GSO pages"""
        # Don't login
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should be redirected to login
        self.assertEqual(response.status_code, 302)
        self.assertIn('login', response.url)


class GSONavigationJavaScriptTestCase(TestCase):
    """Test JavaScript functionality for GSO navigation"""
    
    def setUp(self):
        """Set up test data"""
        self.client = Client()
        
        self.gso_user = User.objects.create_user(
            username='gso_staff',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.gso_profile = UserProfile.objects.create(
            user=self.gso_user,
            role='gso_staff',
            department='GSO'
        )

    def test_navigation_javascript_functions_present(self):
        """Test that navigation JavaScript functions are included"""
        self.client.login(username='gso_staff', password='testpass123')

        response = self.client.get(reverse('gso_dashboard_main'))

        # Should include the GSO navigation JavaScript file
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'gso_navigation.js')

    def test_navigation_css_classes_present(self):
        """Test that navigation CSS classes are defined"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should include navigation CSS
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'nav-link.active')
        self.assertContains(response, 'nav-sublink.active')
        self.assertContains(response, 'nav-submenu')

    def test_navigation_data_attributes_present(self):
        """Test that navigation elements have proper data attributes"""
        self.client.login(username='gso_staff', password='testpass123')
        
        response = self.client.get(reverse('gso_dashboard_main'))
        
        # Should include data attributes for navigation
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'data-nav-item')
        self.assertContains(response, 'data-nav-section')
