{% extends 'admin_dashboard.html' %}

{% block title %}Role Management - Admin Dashboard{% endblock %}
{% block page_title %}Role Management{% endblock %}

{% block content %}
<div class="bg-white shadow rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">User Roles & Permissions</h3>
    <p class="text-gray-600">Role management interface will be implemented here.</p>
    
    <div class="mt-6">
        <h4 class="text-md font-medium text-gray-900 mb-2">Current Role Statistics</h4>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            {% for stat in role_stats %}
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="text-2xl font-bold text-gray-900">{{ stat.count }}</div>
                <div class="text-sm text-gray-600">{{ stat.role|title }} Users</div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}
