{% extends 'base.html' %}
{% load static %}

{% block title %}Advanced Inventory Search{% endblock %}
{% block page_title %}Advanced Inventory Search{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Advanced Inventory Search</h1>
            <p class="text-gray-600">Search and filter inventory items with advanced criteria</p>
        </div>
        <div class="flex space-x-2">
            <!-- Save Search Button -->
            <button onclick="showSaveSearchModal()" 
                    class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                </svg>
                Save Search
            </button>
            
            <!-- My Saved Searches -->
            <a href="{% url 'my_saved_searches' %}?type=inventory" 
               class="inline-flex items-center px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 transition-colors">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                </svg>
                My Searches
            </a>
        </div>
    </div>

    <!-- Advanced Search Form -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Search Filters</h3>
        
        <form hx-get="{% url 'advanced_inventory_search' %}" 
              hx-target="#search-results" 
              hx-indicator="#search-loading"
              hx-trigger="change, submit, keyup delay:300ms from:input[name='q']"
              class="space-y-4">
            
            <!-- Text Search -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="md:col-span-2">
                    <label for="q" class="block text-sm font-medium text-gray-700 mb-1">Search Query</label>
                    <div class="relative">
                        <input type="text" 
                               name="q" 
                               id="q"
                               value="{{ search_params.q }}"
                               placeholder="Search by name, description, category, or QR code..."
                               class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Filters Row 1 -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Category Filter -->
                <div>
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
                    <select name="category" id="category" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.id }}" {% if search_params.category == category.id|stringformat:"s" %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                        {% endfor %}
                    </select>
                </div>
                
                <!-- Stock Status Filter -->
                <div>
                    <label for="stock_status" class="block text-sm font-medium text-gray-700 mb-1">Stock Status</label>
                    <select name="stock_status" id="stock_status" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="">All Items</option>
                        <option value="normal" {% if search_params.stock_status == 'normal' %}selected{% endif %}>Normal Stock</option>
                        <option value="low_stock" {% if search_params.stock_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                        <option value="out_of_stock" {% if search_params.stock_status == 'out_of_stock' %}selected{% endif %}>Out of Stock</option>
                        <option value="overstocked" {% if search_params.stock_status == 'overstocked' %}selected{% endif %}>Overstocked</option>
                    </select>
                </div>
                
                <!-- Date Added From -->
                <div>
                    <label for="date_added_from" class="block text-sm font-medium text-gray-700 mb-1">Added From</label>
                    <input type="date" 
                           name="date_added_from" 
                           id="date_added_from"
                           value="{{ search_params.date_added_from }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <!-- Date Added To -->
                <div>
                    <label for="date_added_to" class="block text-sm font-medium text-gray-700 mb-1">Added To</label>
                    <input type="date" 
                           name="date_added_to" 
                           id="date_added_to"
                           value="{{ search_params.date_added_to }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
            
            <!-- Sort Options -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
                    <select name="sort" id="sort" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="name" {% if search_params.sort == 'name' %}selected{% endif %}>Name (A-Z)</option>
                        <option value="name_desc" {% if search_params.sort == 'name_desc' %}selected{% endif %}>Name (Z-A)</option>
                        <option value="category" {% if search_params.sort == 'category' %}selected{% endif %}>Category</option>
                        <option value="stock_asc" {% if search_params.sort == 'stock_asc' %}selected{% endif %}>Stock (Low to High)</option>
                        <option value="stock_desc" {% if search_params.sort == 'stock_desc' %}selected{% endif %}>Stock (High to Low)</option>
                        <option value="usage_desc" {% if search_params.sort == 'usage_desc' %}selected{% endif %}>Most Requested</option>
                        <option value="updated" {% if search_params.sort == 'updated' %}selected{% endif %}>Recently Updated</option>
                        <option value="created" {% if search_params.sort == 'created' %}selected{% endif %}>Recently Added</option>
                    </select>
                </div>
                
                <!-- Clear Filters -->
                <div class="flex items-end">
                    <a href="{% url 'advanced_inventory_search' %}" 
                       class="w-full text-center px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors">
                        Clear All Filters
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Loading Indicator -->
    <div id="search-loading" class="htmx-indicator">
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Searching...</span>
        </div>
    </div>

    <!-- Search Results -->
    <div id="search-results">
        {% include 'search/partials/inventory_results.html' %}
    </div>
</div>

<!-- Save Search Modal -->
<div id="save-search-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Save Search</h3>
            <form id="save-search-form">
                <div class="mb-4">
                    <label for="search-name" class="block text-sm font-medium text-gray-700 mb-1">Search Name</label>
                    <input type="text" id="search-name" name="name" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                           placeholder="Enter a name for this search">
                </div>
                <div class="mb-4">
                    <label class="flex items-center">
                        <input type="checkbox" id="search-public" name="is_public" class="mr-2">
                        <span class="text-sm text-gray-700">Make this search public (visible to other users)</span>
                    </label>
                </div>
                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="hideSaveSearchModal()" 
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                        Save Search
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showSaveSearchModal() {
    document.getElementById('save-search-modal').classList.remove('hidden');
}

function hideSaveSearchModal() {
    document.getElementById('save-search-modal').classList.add('hidden');
}

document.getElementById('save-search-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(document.querySelector('form[hx-get]'));
    const searchParams = {};
    for (let [key, value] of formData.entries()) {
        if (value) searchParams[key] = value;
    }
    
    const saveData = {
        name: document.getElementById('search-name').value,
        search_type: 'inventory',
        search_params: searchParams,
        is_public: document.getElementById('search-public').checked
    };
    
    fetch('{% url "save_search" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(saveData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Search saved successfully!');
            hideSaveSearchModal();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        alert('Error saving search: ' + error);
    });
});
</script>
{% endblock %}
