# Generated by Django 4.2.17 on 2025-07-29 04:00

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('suptrack', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SavedSearch',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('search_type', models.CharField(choices=[('inventory', 'Inventory Items'), ('requests', 'Supply Requests'), ('scan_logs', 'QR Scan Logs'), ('users', 'Users')], max_length=20)),
                ('search_params', models.TextField()),
                ('is_public', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('usage_count', models.PositiveIntegerField(default=0)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='saved_searches', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Saved Search',
                'verbose_name_plural': 'Saved Searches',
                'ordering': ['-last_used', '-created_at'],
                'indexes': [models.Index(fields=['user', 'search_type'], name='suptrack_sa_user_id_64b145_idx'), models.Index(fields=['is_public', 'search_type'], name='suptrack_sa_is_publ_e8b0b4_idx')],
                'unique_together': {('user', 'name', 'search_type')},
            },
        ),
    ]
