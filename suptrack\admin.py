from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.urls import reverse
from django.db.models import Count, Sum, Q
from django.utils import timezone
from datetime import timedelta
import csv
from django.http import HttpResponse

from .models import (
    UserProfile, SupplyCategory, SupplyItem, SupplyRequest,
    RequestItem, QRScanLog, InventoryTransaction, SavedSearch, SystemConfiguration,
    Notification, NotificationPreference
)


# Inline classes for related models
class UserProfileInline(admin.StackedInline):
    """Inline admin for UserProfile"""
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'
    fields = ('role', 'department', 'phone_number')


class RequestItemInline(admin.TabularInline):
    """Inline admin for RequestItem"""
    model = RequestItem
    extra = 0
    fields = ('supply_item', 'quantity_requested', 'quantity_approved', 'notes')
    readonly_fields = ('quantity_approved',)


class InventoryTransactionInline(admin.TabularInline):
    """Inline admin for InventoryTransaction"""
    model = InventoryTransaction
    extra = 0
    fields = ('transaction_date', 'transaction_type', 'quantity', 'performed_by', 'reference_number')
    readonly_fields = ('transaction_date', 'performed_by')


class QRScanLogInline(admin.TabularInline):
    """Inline admin for QRScanLog"""
    model = QRScanLog
    extra = 0
    fields = ('scan_datetime', 'scan_type', 'scanned_by', 'location', 'notes')
    readonly_fields = ('scan_datetime', 'scanned_by')


# Custom User Admin
class UserAdmin(BaseUserAdmin):
    """Enhanced User admin with UserProfile integration"""
    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'first_name', 'last_name', 'get_role', 'get_department', 'is_active', 'date_joined')
    list_filter = ('is_active', 'is_staff', 'is_superuser', 'userprofile__role', 'userprofile__department')
    search_fields = ('username', 'first_name', 'last_name', 'email', 'userprofile__department')
    ordering = ('-date_joined',)

    def get_role(self, obj):
        """Get user role from profile"""
        try:
            return obj.userprofile.get_role_display()
        except UserProfile.DoesNotExist:
            return 'No Profile'
    get_role.short_description = 'Role'
    get_role.admin_order_field = 'userprofile__role'

    def get_department(self, obj):
        """Get user department from profile"""
        try:
            return obj.userprofile.department
        except UserProfile.DoesNotExist:
            return 'No Profile'
    get_department.short_description = 'Department'
    get_department.admin_order_field = 'userprofile__department'

    actions = ['activate_users', 'deactivate_users', 'export_users_csv']

    def activate_users(self, request, queryset):
        """Bulk activate users"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} users were successfully activated.')
    activate_users.short_description = "Activate selected users"

    def deactivate_users(self, request, queryset):
        """Bulk deactivate users"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} users were successfully deactivated.')
    deactivate_users.short_description = "Deactivate selected users"

    def export_users_csv(self, request, queryset):
        """Export users to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="users.csv"'

        writer = csv.writer(response)
        writer.writerow(['Username', 'Email', 'First Name', 'Last Name', 'Role', 'Department', 'Active', 'Date Joined'])

        for user in queryset:
            try:
                role = user.userprofile.get_role_display()
                department = user.userprofile.department
            except UserProfile.DoesNotExist:
                role = 'No Profile'
                department = 'No Profile'

            writer.writerow([
                user.username, user.email, user.first_name, user.last_name,
                role, department, user.is_active, user.date_joined.strftime('%Y-%m-%d')
            ])

        return response
    export_users_csv.short_description = "Export selected users to CSV"


# UserProfile Admin
@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    """Admin interface for UserProfile"""
    list_display = ('user', 'role', 'department', 'phone_number', 'created_at')
    list_filter = ('role', 'department', 'created_at')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'department')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('User Information', {
            'fields': ('user',)
        }),
        ('Profile Details', {
            'fields': ('role', 'department', 'phone_number')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['change_role_to_admin', 'change_role_to_gso', 'change_role_to_user']

    def change_role_to_admin(self, request, queryset):
        """Change role to admin"""
        updated = queryset.update(role='admin')
        self.message_user(request, f'{updated} profiles changed to admin role.')
    change_role_to_admin.short_description = "Change role to Admin"

    def change_role_to_gso(self, request, queryset):
        """Change role to GSO staff"""
        updated = queryset.update(role='gso_staff')
        self.message_user(request, f'{updated} profiles changed to GSO staff role.')
    change_role_to_gso.short_description = "Change role to GSO Staff"

    def change_role_to_user(self, request, queryset):
        """Change role to department user"""
        updated = queryset.update(role='department_user')
        self.message_user(request, f'{updated} profiles changed to department user role.')
    change_role_to_user.short_description = "Change role to Department User"


# SupplyCategory Admin
@admin.register(SupplyCategory)
class SupplyCategoryAdmin(admin.ModelAdmin):
    """Admin interface for SupplyCategory"""
    list_display = ('name', 'description', 'item_count', 'created_at')
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at',)

    def item_count(self, obj):
        """Get count of items in this category"""
        return obj.items.count()
    item_count.short_description = 'Items Count'

    def get_queryset(self, request):
        """Optimize queryset with item count"""
        return super().get_queryset(request).annotate(
            items_count=Count('items')
        )


# SupplyItem Admin
@admin.register(SupplyItem)
class SupplyItemAdmin(admin.ModelAdmin):
    """Admin interface for SupplyItem"""
    list_display = ('name', 'category', 'current_stock', 'minimum_stock', 'stock_status', 'unit_of_measure', 'updated_at')
    list_filter = ('category', 'unit_of_measure', 'created_at')
    search_fields = ('name', 'description', 'qr_code_data')
    ordering = ('name',)
    readonly_fields = ('qr_code_data', 'created_at', 'updated_at', 'qr_code_preview')
    inlines = [InventoryTransactionInline, QRScanLogInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'description', 'category', 'unit_of_measure')
        }),
        ('Stock Information', {
            'fields': ('current_stock', 'minimum_stock')
        }),
        ('QR Code', {
            'fields': ('qr_code', 'qr_code_data', 'qr_code_preview'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def stock_status(self, obj):
        """Display stock status with color coding"""
        if obj.current_stock == 0:
            return format_html('<span style="color: red; font-weight: bold;">Out of Stock</span>')
        elif obj.current_stock <= obj.minimum_stock:
            return format_html('<span style="color: orange; font-weight: bold;">Low Stock</span>')
        else:
            return format_html('<span style="color: green;">Normal</span>')
    stock_status.short_description = 'Stock Status'

    def qr_code_preview(self, obj):
        """Display QR code preview"""
        if obj.qr_code:
            return format_html('<img src="{}" width="100" height="100" />', obj.qr_code.url)
        return "No QR Code"
    qr_code_preview.short_description = 'QR Code Preview'

    actions = ['bulk_stock_adjustment', 'generate_qr_codes', 'export_inventory_csv', 'mark_low_stock']

    def bulk_stock_adjustment(self, request, queryset):
        """Bulk stock adjustment action"""
        # This would typically redirect to a custom form
        selected = queryset.values_list('id', flat=True)
        return HttpResponse(f"Selected {len(selected)} items for bulk stock adjustment. Feature coming soon.")
    bulk_stock_adjustment.short_description = "Bulk stock adjustment"

    def generate_qr_codes(self, request, queryset):
        """Generate QR codes for selected items"""
        count = 0
        for item in queryset:
            if not item.qr_code_data:
                item.generate_qr_code()
                count += 1
        self.message_user(request, f'Generated QR codes for {count} items.')
    generate_qr_codes.short_description = "Generate QR codes"

    def export_inventory_csv(self, request, queryset):
        """Export inventory to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="inventory.csv"'

        writer = csv.writer(response)
        writer.writerow(['Name', 'Category', 'Current Stock', 'Minimum Stock', 'Unit', 'QR Code', 'Created'])

        for item in queryset:
            writer.writerow([
                item.name, item.category.name, item.current_stock,
                item.minimum_stock, item.unit_of_measure, item.qr_code_data,
                item.created_at.strftime('%Y-%m-%d')
            ])

        return response
    export_inventory_csv.short_description = "Export to CSV"

    def mark_low_stock(self, request, queryset):
        """Mark items as low stock (for testing)"""
        updated = queryset.update(current_stock=1)
        self.message_user(request, f'{updated} items marked as low stock.')
    mark_low_stock.short_description = "Mark as low stock (testing)"


# SupplyRequest Admin
@admin.register(SupplyRequest)
class SupplyRequestAdmin(admin.ModelAdmin):
    """Admin interface for SupplyRequest"""
    list_display = ('request_number', 'requester', 'department', 'status', 'item_count', 'request_date', 'approved_date')
    list_filter = ('status', 'department', 'request_date', 'approved_date')
    search_fields = ('request_number', 'requester__username', 'requester__first_name', 'requester__last_name', 'department')
    ordering = ('-request_date',)
    readonly_fields = ('request_number', 'request_date', 'approved_date')
    inlines = [RequestItemInline]

    fieldsets = (
        ('Request Information', {
            'fields': ('request_number', 'requester', 'department', 'purpose')
        }),
        ('Status', {
            'fields': ('status', 'priority')
        }),
        ('Approval Information', {
            'fields': ('approved_by', 'approved_date', 'rejection_reason'),
            'classes': ('collapse',)
        }),

        ('Timestamps', {
            'fields': ('request_date',),
            'classes': ('collapse',)
        }),
    )

    def item_count(self, obj):
        """Get count of items in this request"""
        return obj.items.count()
    item_count.short_description = 'Items'

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related(
            'requester', 'approved_by'
        ).prefetch_related('items')

    actions = ['approve_requests', 'reject_requests', 'export_requests_csv']

    def approve_requests(self, request, queryset):
        """Bulk approve requests"""
        pending_requests = queryset.filter(status='pending')
        updated = pending_requests.update(
            status='approved',
            approved_by=request.user,
            approved_date=timezone.now()
        )
        self.message_user(request, f'{updated} requests were approved.')
    approve_requests.short_description = "Approve selected requests"

    def reject_requests(self, request, queryset):
        """Bulk reject requests"""
        pending_requests = queryset.filter(status='pending')
        updated = pending_requests.update(
            status='rejected',
            approved_by=request.user,
            approved_date=timezone.now(),
            rejection_reason='Bulk rejection via admin'
        )
        self.message_user(request, f'{updated} requests were rejected.')
    reject_requests.short_description = "Reject selected requests"

    def export_requests_csv(self, request, queryset):
        """Export requests to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="requests.csv"'

        writer = csv.writer(response)
        writer.writerow(['Request Number', 'Requester', 'Department', 'Status', 'Items', 'Request Date', 'Approved Date'])

        for req in queryset:
            writer.writerow([
                req.request_number, req.requester.get_full_name() or req.requester.username,
                req.department, req.get_status_display(), req.items.count(),
                req.request_date.strftime('%Y-%m-%d'),
                req.approved_date.strftime('%Y-%m-%d') if req.approved_date else ''
            ])

        return response
    export_requests_csv.short_description = "Export to CSV"


# QRScanLog Admin
@admin.register(QRScanLog)
class QRScanLogAdmin(admin.ModelAdmin):
    """Admin interface for QRScanLog"""
    list_display = ('supply_item', 'scan_type', 'scanned_by', 'location', 'scan_datetime')
    list_filter = ('scan_type', 'scan_datetime', 'location')
    search_fields = ('supply_item__name', 'scanned_by__username', 'location', 'notes')
    ordering = ('-scan_datetime',)
    readonly_fields = ('scan_datetime',)

    fieldsets = (
        ('Scan Information', {
            'fields': ('supply_item', 'scan_type', 'scanned_by', 'scan_datetime')
        }),
        ('Location and Notes', {
            'fields': ('location', 'notes')
        }),
        ('Related Request', {
            'fields': ('request_item',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related(
            'supply_item', 'scanned_by', 'request_item'
        )

    actions = ['export_scan_logs_csv']

    def export_scan_logs_csv(self, request, queryset):
        """Export scan logs to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="scan_logs.csv"'

        writer = csv.writer(response)
        writer.writerow(['Item', 'Scan Type', 'Scanned By', 'Location', 'Date/Time', 'Notes'])

        for log in queryset:
            writer.writerow([
                log.supply_item.name, log.get_scan_type_display(),
                log.scanned_by.get_full_name() or log.scanned_by.username,
                log.location, log.scan_datetime.strftime('%Y-%m-%d %H:%M:%S'),
                log.notes
            ])

        return response
    export_scan_logs_csv.short_description = "Export to CSV"


# InventoryTransaction Admin
@admin.register(InventoryTransaction)
class InventoryTransactionAdmin(admin.ModelAdmin):
    """Admin interface for InventoryTransaction"""
    list_display = ('supply_item', 'transaction_type', 'quantity', 'previous_stock', 'new_stock', 'performed_by', 'transaction_date')
    list_filter = ('transaction_type', 'transaction_date')
    search_fields = ('supply_item__name', 'performed_by__username', 'reference_number', 'notes')
    ordering = ('-transaction_date',)
    readonly_fields = ('transaction_date',)

    fieldsets = (
        ('Transaction Information', {
            'fields': ('supply_item', 'transaction_type', 'quantity', 'performed_by', 'transaction_date')
        }),
        ('Stock Changes', {
            'fields': ('previous_stock', 'new_stock')
        }),
        ('Reference and Notes', {
            'fields': ('reference_number', 'notes')
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related(
            'supply_item', 'performed_by'
        )

    actions = ['export_transactions_csv']

    def export_transactions_csv(self, request, queryset):
        """Export transactions to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="transactions.csv"'

        writer = csv.writer(response)
        writer.writerow(['Item', 'Type', 'Quantity', 'Previous Stock', 'New Stock', 'Performed By', 'Date', 'Reference'])

        for transaction in queryset:
            writer.writerow([
                transaction.supply_item.name, transaction.get_transaction_type_display(),
                transaction.quantity, transaction.previous_stock, transaction.new_stock,
                transaction.performed_by.get_full_name() or transaction.performed_by.username,
                transaction.transaction_date.strftime('%Y-%m-%d %H:%M:%S'),
                transaction.reference_number
            ])

        return response
    export_transactions_csv.short_description = "Export to CSV"


# SavedSearch Admin
@admin.register(SavedSearch)
class SavedSearchAdmin(admin.ModelAdmin):
    """Admin interface for SavedSearch"""
    list_display = ('name', 'user', 'search_type', 'is_public', 'usage_count', 'last_used', 'created_at')
    list_filter = ('search_type', 'is_public', 'created_at')
    search_fields = ('name', 'user__username')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at', 'usage_count', 'last_used')

    fieldsets = (
        ('Search Information', {
            'fields': ('name', 'user', 'search_type', 'is_public')
        }),
        ('Search Parameters', {
            'fields': ('search_params',),
            'classes': ('collapse',)
        }),
        ('Usage Statistics', {
            'fields': ('usage_count', 'last_used'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['make_public', 'make_private', 'reset_usage_count']

    def make_public(self, request, queryset):
        """Make searches public"""
        updated = queryset.update(is_public=True)
        self.message_user(request, f'{updated} searches made public.')
    make_public.short_description = "Make selected searches public"

    def make_private(self, request, queryset):
        """Make searches private"""
        updated = queryset.update(is_public=False)
        self.message_user(request, f'{updated} searches made private.')
    make_private.short_description = "Make selected searches private"

    def reset_usage_count(self, request, queryset):
        """Reset usage count"""
        updated = queryset.update(usage_count=0, last_used=None)
        self.message_user(request, f'Usage count reset for {updated} searches.')
    reset_usage_count.short_description = "Reset usage count"


# RequestItem Admin
@admin.register(RequestItem)
class RequestItemAdmin(admin.ModelAdmin):
    """Admin interface for RequestItem"""
    list_display = ('request', 'supply_item', 'quantity_requested', 'quantity_approved', 'approval_status')
    list_filter = ('request__status', 'supply_item__category')
    search_fields = ('request__request_number', 'supply_item__name', 'notes')
    ordering = ('-request__request_date',)

    def approval_status(self, obj):
        """Display approval status"""
        if obj.quantity_approved is None:
            return format_html('<span style="color: orange;">Pending</span>')
        elif obj.quantity_approved == obj.quantity_requested:
            return format_html('<span style="color: green;">Fully Approved</span>')
        elif obj.quantity_approved > 0:
            return format_html('<span style="color: blue;">Partially Approved</span>')
        else:
            return format_html('<span style="color: red;">Rejected</span>')
    approval_status.short_description = 'Approval Status'

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related(
            'request', 'supply_item'
        )


# SystemConfiguration Admin
@admin.register(SystemConfiguration)
class SystemConfigurationAdmin(admin.ModelAdmin):
    """Admin interface for SystemConfiguration"""
    list_display = ('key', 'value_preview', 'setting_type', 'category', 'is_active', 'updated_at', 'updated_by')
    list_filter = ('setting_type', 'category', 'is_active', 'updated_at')
    search_fields = ('key', 'description', 'value')
    ordering = ('category', 'key')
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('Setting Information', {
            'fields': ('key', 'value', 'setting_type', 'category')
        }),
        ('Description', {
            'fields': ('description',)
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'updated_by'),
            'classes': ('collapse',)
        }),
    )

    def value_preview(self, obj):
        """Show truncated value"""
        if len(obj.value) > 50:
            return obj.value[:50] + '...'
        return obj.value
    value_preview.short_description = 'Value'

    def save_model(self, request, obj, form, change):
        """Set updated_by when saving"""
        obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    actions = ['activate_settings', 'deactivate_settings', 'export_settings_csv']

    def activate_settings(self, request, queryset):
        """Activate settings"""
        updated = queryset.update(is_active=True)
        self.message_user(request, f'{updated} settings activated.')
    activate_settings.short_description = "Activate selected settings"

    def deactivate_settings(self, request, queryset):
        """Deactivate settings"""
        updated = queryset.update(is_active=False)
        self.message_user(request, f'{updated} settings deactivated.')
    deactivate_settings.short_description = "Deactivate selected settings"

    def export_settings_csv(self, request, queryset):
        """Export settings to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="system_settings.csv"'

        writer = csv.writer(response)
        writer.writerow(['Key', 'Value', 'Type', 'Category', 'Description', 'Active', 'Updated'])

        for setting in queryset:
            writer.writerow([
                setting.key, setting.value, setting.setting_type,
                setting.category, setting.description, setting.is_active,
                setting.updated_at.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response
    export_settings_csv.short_description = "Export to CSV"


# Notification Admin
@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    """Admin interface for Notification"""
    list_display = ('title', 'recipient', 'notification_type', 'priority', 'is_read', 'is_dismissed', 'created_at')
    list_filter = ('notification_type', 'priority', 'is_read', 'is_dismissed', 'created_at')
    search_fields = ('title', 'message', 'recipient__username', 'recipient__first_name', 'recipient__last_name')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'read_at')

    fieldsets = (
        ('Notification Details', {
            'fields': ('recipient', 'notification_type', 'title', 'message', 'priority')
        }),
        ('Related Objects', {
            'fields': ('supply_item', 'supply_request'),
            'classes': ('collapse',)
        }),
        ('Action', {
            'fields': ('action_url', 'action_text'),
            'classes': ('collapse',)
        }),
        ('Status', {
            'fields': ('is_read', 'is_dismissed', 'read_at', 'expires_at')
        }),
        ('Timestamps', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def get_queryset(self, request):
        """Optimize queryset"""
        return super().get_queryset(request).select_related(
            'recipient', 'supply_item', 'supply_request'
        )

    actions = ['mark_as_read', 'mark_as_unread', 'dismiss_notifications', 'export_notifications_csv']

    def mark_as_read(self, request, queryset):
        """Mark notifications as read"""
        updated = queryset.update(is_read=True, read_at=timezone.now())
        self.message_user(request, f'{updated} notifications marked as read.')
    mark_as_read.short_description = "Mark selected notifications as read"

    def mark_as_unread(self, request, queryset):
        """Mark notifications as unread"""
        updated = queryset.update(is_read=False, read_at=None)
        self.message_user(request, f'{updated} notifications marked as unread.')
    mark_as_unread.short_description = "Mark selected notifications as unread"

    def dismiss_notifications(self, request, queryset):
        """Dismiss notifications"""
        updated = queryset.update(is_dismissed=True)
        self.message_user(request, f'{updated} notifications dismissed.')
    dismiss_notifications.short_description = "Dismiss selected notifications"

    def export_notifications_csv(self, request, queryset):
        """Export notifications to CSV"""
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename="notifications.csv"'

        writer = csv.writer(response)
        writer.writerow(['Title', 'Recipient', 'Type', 'Priority', 'Message', 'Read', 'Created'])

        for notification in queryset:
            writer.writerow([
                notification.title,
                notification.recipient.get_full_name() or notification.recipient.username,
                notification.get_notification_type_display(),
                notification.get_priority_display(),
                notification.message,
                'Yes' if notification.is_read else 'No',
                notification.created_at.strftime('%Y-%m-%d %H:%M:%S')
            ])

        return response
    export_notifications_csv.short_description = "Export to CSV"


# NotificationPreference Admin
@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    """Admin interface for NotificationPreference"""
    list_display = ('user', 'low_stock_alerts', 'request_status_updates', 'in_app_notifications', 'email_notifications', 'digest_frequency')
    list_filter = ('low_stock_alerts', 'request_status_updates', 'in_app_notifications', 'email_notifications', 'digest_frequency')
    search_fields = ('user__username', 'user__first_name', 'user__last_name')
    ordering = ('user__username',)
    readonly_fields = ('created_at', 'updated_at')

    fieldsets = (
        ('User', {
            'fields': ('user',)
        }),
        ('Notification Types', {
            'fields': ('low_stock_alerts', 'out_of_stock_alerts', 'request_status_updates', 'system_alerts', 'maintenance_notices')
        }),
        ('Delivery Preferences', {
            'fields': ('in_app_notifications', 'email_notifications', 'digest_frequency')
        }),
        ('Quiet Hours', {
            'fields': ('quiet_hours_enabled', 'quiet_hours_start', 'quiet_hours_end'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    actions = ['enable_all_notifications', 'disable_email_notifications', 'reset_to_defaults']

    def enable_all_notifications(self, request, queryset):
        """Enable all notification types"""
        updated = queryset.update(
            low_stock_alerts=True,
            out_of_stock_alerts=True,
            request_status_updates=True,
            system_alerts=True,
            maintenance_notices=True,
            in_app_notifications=True
        )
        self.message_user(request, f'Enabled all notifications for {updated} users.')
    enable_all_notifications.short_description = "Enable all notifications"

    def disable_email_notifications(self, request, queryset):
        """Disable email notifications"""
        updated = queryset.update(email_notifications=False)
        self.message_user(request, f'Disabled email notifications for {updated} users.')
    disable_email_notifications.short_description = "Disable email notifications"

    def reset_to_defaults(self, request, queryset):
        """Reset preferences to defaults"""
        updated = queryset.update(
            low_stock_alerts=True,
            out_of_stock_alerts=True,
            request_status_updates=True,
            system_alerts=True,
            maintenance_notices=True,
            in_app_notifications=True,
            email_notifications=False,
            digest_frequency='immediate',
            quiet_hours_enabled=False
        )
        self.message_user(request, f'Reset preferences to defaults for {updated} users.')
    reset_to_defaults.short_description = "Reset to default preferences"


# Customize admin site
admin.site.site_header = "Smart Supply Management Admin"
admin.site.site_title = "Smart Supply Admin"
admin.site.index_title = "Welcome to Smart Supply Management System"

# Unregister the default User admin and register our custom one
admin.site.unregister(User)
admin.site.register(User, UserAdmin)
