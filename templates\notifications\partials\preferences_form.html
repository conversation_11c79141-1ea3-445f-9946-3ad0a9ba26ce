<div id="preferences-form">
    {% if success_message %}
        <div class="p-4 bg-green-50 border-l-4 border-green-400">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm text-green-700">{{ success_message }}</p>
                </div>
            </div>
        </div>
    {% endif %}

    <!-- Notification Types -->
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Notification Types</h3>
        <p class="text-sm text-gray-600 mb-6">Choose which types of notifications you want to receive</p>
        
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <label for="low_stock_alerts" class="text-sm font-medium text-gray-700">Low Stock Alerts</label>
                    <p class="text-sm text-gray-500">Get notified when inventory items are running low</p>
                </div>
                <input type="checkbox" 
                       id="low_stock_alerts" 
                       name="low_stock_alerts" 
                       {% if preferences.low_stock_alerts %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <label for="out_of_stock_alerts" class="text-sm font-medium text-gray-700">Out of Stock Alerts</label>
                    <p class="text-sm text-gray-500">Get notified when inventory items are completely out of stock</p>
                </div>
                <input type="checkbox" 
                       id="out_of_stock_alerts" 
                       name="out_of_stock_alerts" 
                       {% if preferences.out_of_stock_alerts %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <label for="request_status_updates" class="text-sm font-medium text-gray-700">Request Status Updates</label>
                    <p class="text-sm text-gray-500">Get notified when your supply requests are approved, rejected, or released</p>
                </div>
                <input type="checkbox" 
                       id="request_status_updates" 
                       name="request_status_updates" 
                       {% if preferences.request_status_updates %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <label for="system_alerts" class="text-sm font-medium text-gray-700">System Alerts</label>
                    <p class="text-sm text-gray-500">Get notified about important system announcements and alerts</p>
                </div>
                <input type="checkbox" 
                       id="system_alerts" 
                       name="system_alerts" 
                       {% if preferences.system_alerts %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <label for="maintenance_notices" class="text-sm font-medium text-gray-700">Maintenance Notices</label>
                    <p class="text-sm text-gray-500">Get notified about scheduled maintenance and system downtime</p>
                </div>
                <input type="checkbox" 
                       id="maintenance_notices" 
                       name="maintenance_notices" 
                       {% if preferences.maintenance_notices %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
        </div>
    </div>

    <!-- Delivery Preferences -->
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delivery Preferences</h3>
        <p class="text-sm text-gray-600 mb-6">Choose how you want to receive notifications</p>
        
        <div class="space-y-4">
            <div class="flex items-center justify-between">
                <div>
                    <label for="in_app_notifications" class="text-sm font-medium text-gray-700">In-App Notifications</label>
                    <p class="text-sm text-gray-500">Show notifications in the application interface</p>
                </div>
                <input type="checkbox" 
                       id="in_app_notifications" 
                       name="in_app_notifications" 
                       {% if preferences.in_app_notifications %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
            
            <div class="flex items-center justify-between">
                <div>
                    <label for="email_notifications" class="text-sm font-medium text-gray-700">Email Notifications</label>
                    <p class="text-sm text-gray-500">Send notifications to your email address</p>
                </div>
                <input type="checkbox" 
                       id="email_notifications" 
                       name="email_notifications" 
                       {% if preferences.email_notifications %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            </div>
        </div>
    </div>

    <!-- Frequency Settings -->
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Frequency Settings</h3>
        <p class="text-sm text-gray-600 mb-6">Control how often you receive notification digests</p>
        
        <div>
            <label for="digest_frequency" class="block text-sm font-medium text-gray-700 mb-2">Digest Frequency</label>
            <select id="digest_frequency" 
                    name="digest_frequency" 
                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                <option value="immediate" {% if preferences.digest_frequency == 'immediate' %}selected{% endif %}>Immediate</option>
                <option value="hourly" {% if preferences.digest_frequency == 'hourly' %}selected{% endif %}>Hourly</option>
                <option value="daily" {% if preferences.digest_frequency == 'daily' %}selected{% endif %}>Daily</option>
                <option value="weekly" {% if preferences.digest_frequency == 'weekly' %}selected{% endif %}>Weekly</option>
            </select>
            <p class="text-sm text-gray-500 mt-1">Choose how often to receive notification summaries</p>
        </div>
    </div>

    <!-- Quiet Hours -->
    <div class="p-6 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quiet Hours</h3>
        <p class="text-sm text-gray-600 mb-6">Set times when you don't want to receive notifications</p>
        
        <div class="space-y-4">
            <div class="flex items-center">
                <input type="checkbox" 
                       id="quiet_hours_enabled" 
                       name="quiet_hours_enabled" 
                       {% if preferences.quiet_hours_enabled %}checked{% endif %}
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="quiet_hours_enabled" class="ml-2 text-sm font-medium text-gray-700">
                    Enable quiet hours
                </label>
            </div>
            
            <div id="quiet-hours-settings" 
                 class="grid grid-cols-1 md:grid-cols-2 gap-4 {% if not preferences.quiet_hours_enabled %}opacity-50{% endif %}">
                <div>
                    <label for="quiet_hours_start" class="block text-sm font-medium text-gray-700 mb-1">Start Time</label>
                    <input type="time" 
                           id="quiet_hours_start" 
                           name="quiet_hours_start" 
                           value="{{ preferences.quiet_hours_start|time:'H:i' }}"
                           {% if not preferences.quiet_hours_enabled %}disabled{% endif %}
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
                
                <div>
                    <label for="quiet_hours_end" class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                    <input type="time" 
                           id="quiet_hours_end" 
                           name="quiet_hours_end" 
                           value="{{ preferences.quiet_hours_end|time:'H:i' }}"
                           {% if not preferences.quiet_hours_enabled %}disabled{% endif %}
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                </div>
            </div>
        </div>
    </div>

    <!-- Save Button -->
    <div class="p-6">
        <div class="flex justify-end space-x-3">
            <a href="{% url 'notification_center' %}" 
               class="px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                Cancel
            </a>
            <button type="submit" 
                    class="px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                Save Preferences
            </button>
        </div>
    </div>
</div>

<script>
// Toggle quiet hours settings
document.getElementById('quiet_hours_enabled').addEventListener('change', function() {
    const settings = document.getElementById('quiet-hours-settings');
    const startInput = document.getElementById('quiet_hours_start');
    const endInput = document.getElementById('quiet_hours_end');
    
    if (this.checked) {
        settings.classList.remove('opacity-50');
        startInput.disabled = false;
        endInput.disabled = false;
    } else {
        settings.classList.add('opacity-50');
        startInput.disabled = true;
        endInput.disabled = true;
    }
});
</script>
