/**
 * Mobile Sidebar Enhancement Script
 * Provides consistent mobile sidebar behavior across all interfaces
 */

class MobileSidebarManager {
    constructor() {
        this.isInitialized = false;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.swipeThreshold = 50;
        this.velocityThreshold = 0.3;
        this.init();
    }

    /**
     * Initialize mobile sidebar enhancements
     */
    init() {
        if (this.isInitialized) return;
        
        this.setupSwipeGestures();
        this.setupAccessibilityFeatures();
        this.setupPerformanceOptimizations();
        this.setupResponsiveHandling();
        
        this.isInitialized = true;
        console.log('Mobile Sidebar Manager initialized');
    }

    /**
     * Setup swipe gestures for mobile sidebar
     */
    setupSwipeGestures() {
        let startTime = 0;
        let startX = 0;
        
        document.addEventListener('touchstart', (e) => {
            if (window.innerWidth >= 768) return; // Only on mobile
            
            startTime = Date.now();
            startX = e.touches[0].clientX;
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            if (window.innerWidth >= 768) return;
            
            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const deltaX = currentX - this.touchStartX;
            const deltaY = currentY - this.touchStartY;
            
            // Prevent vertical scrolling when swiping horizontally
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
                e.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            if (window.innerWidth >= 768) return;
            
            const endTime = Date.now();
            const endX = e.changedTouches[0].clientX;
            const deltaX = endX - startX;
            const deltaTime = endTime - startTime;
            const velocity = Math.abs(deltaX) / deltaTime;
            
            // Swipe right to open sidebar (from left edge)
            if (startX < 20 && deltaX > this.swipeThreshold && velocity > this.velocityThreshold) {
                this.openSidebar();
            }
            // Swipe left to close sidebar
            else if (deltaX < -this.swipeThreshold && velocity > this.velocityThreshold) {
                this.closeSidebar();
            }
        }, { passive: true });
    }

    /**
     * Setup accessibility features
     */
    setupAccessibilityFeatures() {
        // Trap focus within sidebar when open on mobile
        document.addEventListener('keydown', (e) => {
            if (window.innerWidth >= 768) return;
            
            const sidebar = document.querySelector('.custom-sidebar');
            const isOpen = sidebar?.classList.contains('translate-x-0') || 
                          document.body.getAttribute('x-data')?.includes('sidebarOpen: true');
            
            if (isOpen && e.key === 'Escape') {
                this.closeSidebar();
                e.preventDefault();
            }
        });

        // Announce sidebar state changes to screen readers
        const sidebar = document.querySelector('.custom-sidebar');
        if (sidebar) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isOpen = sidebar.classList.contains('translate-x-0');
                        this.announceStateChange(isOpen);
                    }
                });
            });
            
            observer.observe(sidebar, { attributes: true });
        }
    }

    /**
     * Setup performance optimizations
     */
    setupPerformanceOptimizations() {
        // Debounce resize events
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 150);
        });

        // Optimize scroll performance when sidebar is open
        let scrollTimeout;
        document.addEventListener('scroll', () => {
            if (window.innerWidth >= 768) return;
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const sidebar = document.querySelector('.custom-sidebar');
                const isOpen = sidebar?.classList.contains('translate-x-0');
                
                if (isOpen) {
                    // Reduce scroll momentum on mobile when sidebar is open
                    document.body.style.overscrollBehavior = 'none';
                }
            }, 50);
        }, { passive: true });
    }

    /**
     * Setup responsive handling
     */
    setupResponsiveHandling() {
        const mediaQuery = window.matchMedia('(max-width: 767px)');
        
        const handleMediaChange = (e) => {
            if (!e.matches) {
                // Desktop view - reset mobile-specific styles
                document.body.style.overflow = '';
                document.body.style.overscrollBehavior = '';
                this.closeSidebar();
            }
        };

        mediaQuery.addListener(handleMediaChange);
        handleMediaChange(mediaQuery);
    }

    /**
     * Open sidebar
     */
    openSidebar() {
        // Trigger Alpine.js method if available
        const alpineData = document.body._x_dataStack?.[0];
        if (alpineData && typeof alpineData.toggleSidebar === 'function') {
            if (!alpineData.sidebarOpen) {
                alpineData.toggleSidebar();
            }
        } else {
            // Fallback for non-Alpine implementations
            const sidebar = document.querySelector('.custom-sidebar');
            const overlay = document.querySelector('.mobile-overlay-optimized');
            
            if (sidebar) {
                sidebar.classList.add('translate-x-0', 'open');
                sidebar.classList.remove('-translate-x-full');
            }
            
            if (overlay) {
                overlay.classList.add('active');
            }
            
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Close sidebar
     */
    closeSidebar() {
        // Trigger Alpine.js method if available
        const alpineData = document.body._x_dataStack?.[0];
        if (alpineData && typeof alpineData.closeSidebar === 'function') {
            alpineData.closeSidebar();
        } else {
            // Fallback for non-Alpine implementations
            const sidebar = document.querySelector('.custom-sidebar');
            const overlay = document.querySelector('.mobile-overlay-optimized');
            
            if (sidebar) {
                sidebar.classList.remove('translate-x-0', 'open');
                sidebar.classList.add('-translate-x-full');
            }
            
            if (overlay) {
                overlay.classList.remove('active');
            }
            
            document.body.style.overflow = '';
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (window.innerWidth >= 768) {
            // Desktop view
            document.body.style.overflow = '';
            document.body.style.overscrollBehavior = '';
        }
    }

    /**
     * Announce state changes to screen readers
     */
    announceStateChange(isOpen) {
        const announcement = isOpen ? 'Navigation menu opened' : 'Navigation menu closed';
        
        // Create temporary announcement element
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;
        
        document.body.appendChild(announcer);
        
        // Remove after announcement
        setTimeout(() => {
            document.body.removeChild(announcer);
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.mobileSidebarManager = new MobileSidebarManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileSidebarManager;
}
