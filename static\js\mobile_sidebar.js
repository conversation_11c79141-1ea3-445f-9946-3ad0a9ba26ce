/**
 * Mobile Sidebar Enhancement Script
 * Provides consistent mobile sidebar behavior across all interfaces
 */

class MobileSidebarManager {
    constructor() {
        this.isInitialized = false;
        this.touchStartX = 0;
        this.touchStartY = 0;
        this.swipeThreshold = 50;
        this.velocityThreshold = 0.3;
        this.deviceType = this.detectDeviceType();
        this.init();
    }

    /**
     * Detect device type for optimized behavior
     */
    detectDeviceType() {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent) ||
                        (window.innerWidth >= 768 && window.innerWidth <= 1024);
        const isIOS = /iphone|ipad|ipod/i.test(userAgent);
        const isAndroid = /android/i.test(userAgent);

        return {
            isMobile,
            isTablet,
            isDesktop: !isMobile && !isTablet,
            isIOS,
            isAndroid,
            hasTouch: 'ontouchstart' in window || navigator.maxTouchPoints > 0
        };
    }

    /**
     * Initialize mobile sidebar enhancements
     */
    init() {
        if (this.isInitialized) return;

        this.setupTouchOptimization();
        this.setupSwipeGestures();
        this.setupAccessibilityFeatures();
        this.setupPerformanceOptimizations();
        this.setupResponsiveHandling();

        this.isInitialized = true;
        console.log('Mobile Sidebar Manager initialized');
    }

    /**
     * Setup touch optimization for toggle button
     */
    setupTouchOptimization() {
        // Wait for DOM to be ready
        document.addEventListener('DOMContentLoaded', () => {
            const toggleBtn = document.querySelector('.mobile-toggle-btn');
            if (!toggleBtn || !this.deviceType.hasTouch) return;

            // Device-specific optimizations
            if (this.deviceType.isIOS) {
                this.setupIOSOptimizations(toggleBtn);
            } else if (this.deviceType.isAndroid) {
                this.setupAndroidOptimizations(toggleBtn);
            } else {
                this.setupGenericTouchOptimizations(toggleBtn);
            }
        });
    }

    /**
     * Setup iOS-specific optimizations
     */
    setupIOSOptimizations(toggleBtn) {
        // Prevent iOS double-tap zoom and ensure single tap response
        toggleBtn.addEventListener('touchstart', (e) => {
            toggleBtn.style.backgroundColor = '#d1d5db';
            toggleBtn.style.transform = 'scale(0.95)';
        }, { passive: true });

        toggleBtn.addEventListener('touchend', (e) => {
            setTimeout(() => {
                toggleBtn.style.backgroundColor = '';
                toggleBtn.style.transform = '';
            }, 150);

            // Prevent default to avoid iOS double-tap zoom
            e.preventDefault();
        }, { passive: false });

        toggleBtn.addEventListener('touchcancel', (e) => {
            toggleBtn.style.backgroundColor = '';
            toggleBtn.style.transform = '';
        }, { passive: true });
    }

    /**
     * Setup Android-specific optimizations
     */
    setupAndroidOptimizations(toggleBtn) {
        // Android typically handles touch events well, but add feedback
        toggleBtn.addEventListener('touchstart', (e) => {
            toggleBtn.style.backgroundColor = '#d1d5db';
            toggleBtn.style.transform = 'scale(0.95)';
        }, { passive: true });

        toggleBtn.addEventListener('touchend', (e) => {
            setTimeout(() => {
                toggleBtn.style.backgroundColor = '';
                toggleBtn.style.transform = '';
            }, 100);
        }, { passive: true });
    }

    /**
     * Setup generic touch optimizations
     */
    setupGenericTouchOptimizations(toggleBtn) {
        // Generic touch device optimizations
        toggleBtn.addEventListener('touchstart', (e) => {
            toggleBtn.style.backgroundColor = '#d1d5db';
            toggleBtn.style.transform = 'scale(0.95)';
        }, { passive: true });

        toggleBtn.addEventListener('touchend', (e) => {
            setTimeout(() => {
                toggleBtn.style.backgroundColor = '';
                toggleBtn.style.transform = '';
            }, 150);
        }, { passive: true });
    }

    /**
     * Setup swipe gestures for mobile sidebar
     */
    setupSwipeGestures() {
        let startTime = 0;
        let startX = 0;
        let isToggleButton = false;

        document.addEventListener('touchstart', (e) => {
            if (window.innerWidth >= 768) return; // Only on mobile

            // Check if touch started on toggle button
            const toggleBtn = document.querySelector('.mobile-toggle-btn');
            isToggleButton = toggleBtn && toggleBtn.contains(e.target);

            // Skip swipe detection if touching toggle button
            if (isToggleButton) return;

            startTime = Date.now();
            startX = e.touches[0].clientX;
            this.touchStartX = e.touches[0].clientX;
            this.touchStartY = e.touches[0].clientY;
        }, { passive: true });

        document.addEventListener('touchmove', (e) => {
            if (window.innerWidth >= 768 || isToggleButton) return;

            const currentX = e.touches[0].clientX;
            const currentY = e.touches[0].clientY;
            const deltaX = currentX - this.touchStartX;
            const deltaY = currentY - this.touchStartY;

            // Prevent vertical scrolling when swiping horizontally
            if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 10) {
                e.preventDefault();
            }
        }, { passive: false });

        document.addEventListener('touchend', (e) => {
            if (window.innerWidth >= 768 || isToggleButton) {
                isToggleButton = false;
                return;
            }

            const endTime = Date.now();
            const endX = e.changedTouches[0].clientX;
            const deltaX = endX - startX;
            const deltaTime = endTime - startTime;
            const velocity = Math.abs(deltaX) / deltaTime;

            // Swipe right to open sidebar (from left edge)
            if (startX < 20 && deltaX > this.swipeThreshold && velocity > this.velocityThreshold) {
                this.openSidebar();
            }
            // Swipe left to close sidebar
            else if (deltaX < -this.swipeThreshold && velocity > this.velocityThreshold) {
                this.closeSidebar();
            }

            isToggleButton = false;
        }, { passive: true });
    }

    /**
     * Setup accessibility features
     */
    setupAccessibilityFeatures() {
        // Trap focus within sidebar when open on mobile
        document.addEventListener('keydown', (e) => {
            if (window.innerWidth >= 768) return;
            
            const sidebar = document.querySelector('.custom-sidebar');
            const isOpen = sidebar?.classList.contains('translate-x-0') || 
                          document.body.getAttribute('x-data')?.includes('sidebarOpen: true');
            
            if (isOpen && e.key === 'Escape') {
                this.closeSidebar();
                e.preventDefault();
            }
        });

        // Announce sidebar state changes to screen readers
        const sidebar = document.querySelector('.custom-sidebar');
        if (sidebar) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        const isOpen = sidebar.classList.contains('translate-x-0');
                        this.announceStateChange(isOpen);
                    }
                });
            });
            
            observer.observe(sidebar, { attributes: true });
        }
    }

    /**
     * Setup performance optimizations
     */
    setupPerformanceOptimizations() {
        // Debounce resize events
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResize();
            }, 150);
        });

        // Optimize scroll performance when sidebar is open
        let scrollTimeout;
        document.addEventListener('scroll', () => {
            if (window.innerWidth >= 768) return;
            
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                const sidebar = document.querySelector('.custom-sidebar');
                const isOpen = sidebar?.classList.contains('translate-x-0');
                
                if (isOpen) {
                    // Reduce scroll momentum on mobile when sidebar is open
                    document.body.style.overscrollBehavior = 'none';
                }
            }, 50);
        }, { passive: true });
    }

    /**
     * Setup responsive handling
     */
    setupResponsiveHandling() {
        // Define breakpoints for different device types
        const mobileQuery = window.matchMedia('(max-width: 767px)');
        const tabletQuery = window.matchMedia('(min-width: 768px) and (max-width: 1023px)');
        const desktopQuery = window.matchMedia('(min-width: 1024px)');

        const handleMobileChange = (e) => {
            if (e.matches) {
                // Mobile view - ensure toggle button is visible and functional
                this.enableMobileMode();
            }
        };

        const handleTabletChange = (e) => {
            if (e.matches) {
                // Tablet view - hybrid behavior
                this.enableTabletMode();
            }
        };

        const handleDesktopChange = (e) => {
            if (e.matches) {
                // Desktop view - reset mobile-specific styles
                this.enableDesktopMode();
            }
        };

        // Add listeners for all breakpoints
        mobileQuery.addListener(handleMobileChange);
        tabletQuery.addListener(handleTabletChange);
        desktopQuery.addListener(handleDesktopChange);

        // Initial setup
        handleMobileChange(mobileQuery);
        handleTabletChange(tabletQuery);
        handleDesktopChange(desktopQuery);
    }

    /**
     * Enable mobile-specific behavior
     */
    enableMobileMode() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (toggleBtn) {
            toggleBtn.style.display = 'flex';
            // Ensure touch events are properly handled
            this.optimizeForTouch();
        }
    }

    /**
     * Enable tablet-specific behavior
     */
    enableTabletMode() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (toggleBtn) {
            // Show toggle button on tablets in portrait mode
            const isPortrait = window.innerHeight > window.innerWidth;
            toggleBtn.style.display = isPortrait ? 'flex' : 'none';
        }
    }

    /**
     * Enable desktop-specific behavior
     */
    enableDesktopMode() {
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (toggleBtn) {
            toggleBtn.style.display = 'none';
        }

        // Reset mobile-specific styles
        document.body.style.overflow = '';
        document.body.style.overscrollBehavior = '';
        this.closeSidebar();
    }

    /**
     * Optimize interface for touch devices
     */
    optimizeForTouch() {
        if (!('ontouchstart' in window)) return;

        // Increase touch targets for better accessibility
        const navLinks = document.querySelectorAll('.nav-link, .nav-sublink');
        navLinks.forEach(link => {
            const currentHeight = parseInt(window.getComputedStyle(link).height);
            if (currentHeight < 44) {
                link.style.minHeight = '44px';
                link.style.display = 'flex';
                link.style.alignItems = 'center';
            }
        });
    }

    /**
     * Open sidebar
     */
    openSidebar() {
        // Trigger Alpine.js method if available
        const alpineData = document.body._x_dataStack?.[0];
        if (alpineData && typeof alpineData.toggleSidebar === 'function') {
            if (!alpineData.sidebarOpen) {
                alpineData.toggleSidebar();
            }
        } else {
            // Fallback for non-Alpine implementations
            const sidebar = document.querySelector('.custom-sidebar');
            const overlay = document.querySelector('.mobile-overlay-optimized');
            
            if (sidebar) {
                sidebar.classList.add('translate-x-0', 'open');
                sidebar.classList.remove('-translate-x-full');
            }
            
            if (overlay) {
                overlay.classList.add('active');
            }
            
            document.body.style.overflow = 'hidden';
        }
    }

    /**
     * Close sidebar
     */
    closeSidebar() {
        // Trigger Alpine.js method if available
        const alpineData = document.body._x_dataStack?.[0];
        if (alpineData && typeof alpineData.closeSidebar === 'function') {
            alpineData.closeSidebar();
        } else {
            // Fallback for non-Alpine implementations
            const sidebar = document.querySelector('.custom-sidebar');
            const overlay = document.querySelector('.mobile-overlay-optimized');
            
            if (sidebar) {
                sidebar.classList.remove('translate-x-0', 'open');
                sidebar.classList.add('-translate-x-full');
            }
            
            if (overlay) {
                overlay.classList.remove('active');
            }
            
            document.body.style.overflow = '';
        }
    }

    /**
     * Handle window resize
     */
    handleResize() {
        if (window.innerWidth >= 768) {
            // Desktop view
            document.body.style.overflow = '';
            document.body.style.overscrollBehavior = '';
        }
    }

    /**
     * Announce state changes to screen readers
     */
    announceStateChange(isOpen) {
        const announcement = isOpen ? 'Navigation menu opened' : 'Navigation menu closed';
        
        // Create temporary announcement element
        const announcer = document.createElement('div');
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.className = 'sr-only';
        announcer.textContent = announcement;
        
        document.body.appendChild(announcer);
        
        // Remove after announcement
        setTimeout(() => {
            document.body.removeChild(announcer);
        }, 1000);
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.mobileSidebarManager = new MobileSidebarManager();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileSidebarManager;
}
