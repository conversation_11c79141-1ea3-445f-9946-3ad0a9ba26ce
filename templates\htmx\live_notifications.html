{% if notifications %}
    <div class="space-y-2">
        {% for notification in notifications %}
            <div class="flex items-start p-3 rounded-lg border
                {% if notification.type == 'success' %}bg-green-50 border-green-200
                {% elif notification.type == 'warning' %}bg-yellow-50 border-yellow-200
                {% elif notification.type == 'error' %}bg-red-50 border-red-200
                {% elif notification.type == 'info' %}bg-blue-50 border-blue-200
                {% else %}bg-gray-50 border-gray-200{% endif %}">
                
                <!-- Icon -->
                <div class="flex-shrink-0">
                    {% if notification.icon == 'check-circle' %}
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.icon == 'exclamation-triangle' %}
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.icon == 'x-circle' %}
                        <svg class="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                    {% elif notification.icon == 'clock' %}
                        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                    {% else %}
                        <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                        </svg>
                    {% endif %}
                </div>
                
                <!-- Content -->
                <div class="ml-3 flex-1">
                    <p class="text-sm font-medium
                        {% if notification.type == 'success' %}text-green-800
                        {% elif notification.type == 'warning' %}text-yellow-800
                        {% elif notification.type == 'error' %}text-red-800
                        {% elif notification.type == 'info' %}text-blue-800
                        {% else %}text-gray-800{% endif %}">
                        {{ notification.message }}
                    </p>
                    
                    {% if notification.url %}
                        <div class="mt-1">
                            <a href="{{ notification.url }}" 
                               class="text-xs font-medium
                                   {% if notification.type == 'success' %}text-green-600 hover:text-green-500
                                   {% elif notification.type == 'warning' %}text-yellow-600 hover:text-yellow-500
                                   {% elif notification.type == 'error' %}text-red-600 hover:text-red-500
                                   {% elif notification.type == 'info' %}text-blue-600 hover:text-blue-500
                                   {% else %}text-gray-600 hover:text-gray-500{% endif %}">
                                View details →
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Dismiss button -->
                <div class="ml-4 flex-shrink-0">
                    <button type="button" 
                            onclick="this.parentElement.parentElement.remove()"
                            class="inline-flex rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2
                                {% if notification.type == 'success' %}text-green-500 hover:bg-green-100 focus:ring-green-600
                                {% elif notification.type == 'warning' %}text-yellow-500 hover:bg-yellow-100 focus:ring-yellow-600
                                {% elif notification.type == 'error' %}text-red-500 hover:bg-red-100 focus:ring-red-600
                                {% elif notification.type == 'info' %}text-blue-500 hover:bg-blue-100 focus:ring-blue-600
                                {% else %}text-gray-500 hover:bg-gray-100 focus:ring-gray-600{% endif %}">
                        <span class="sr-only">Dismiss</span>
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        {% endfor %}
    </div>
{% else %}
    <div class="text-center py-4">
        <svg class="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM4 19h10a2 2 0 002-2V7a2 2 0 00-2-2H4a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
        </svg>
        <p class="text-sm text-gray-500">No new notifications</p>
    </div>
{% endif %}
