{% extends 'base.html' %}

{% block title %}Pending Requests - Smart Supply Management{% endblock %}

{% block page_title %}Pending Supply Requests{% endblock %}

{% block content %}
{% csrf_token %}
<!-- Search and Filter Section -->
<div class="bg-white shadow rounded-lg mb-6">
    <div class="px-4 py-5 sm:p-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4 lg:mb-0">
                Filter Requests ({{ total_pending }} pending)
            </h3>
            <div class="flex flex-col sm:flex-row gap-2">
                <button id="bulk-approve-btn" 
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Bulk Approve (<span id="selected-count">0</span>)
                </button>
                <button hx-get="{% url 'pending_requests_table_htmx' %}" 
                        hx-target="#requests-table"
                        hx-include="[name='search'], [name='department'], [name='date_from'], [name='date_to']"
                        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh
                </button>
            </div>
        </div>
        
        <!-- Filter Form -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
                <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="{{ search_query }}"
                       placeholder="Request #, requester, department..."
                       hx-get="{% url 'pending_requests_table_htmx' %}"
                       hx-target="#requests-table"
                       hx-trigger="keyup changed delay:500ms"
                       hx-include="[name='department'], [name='date_from'], [name='date_to']"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            
            <div>
                <label for="department" class="block text-sm font-medium text-gray-700">Department</label>
                <select name="department" 
                        id="department"
                        hx-get="{% url 'pending_requests_table_htmx' %}"
                        hx-target="#requests-table"
                        hx-trigger="change"
                        hx-include="[name='search'], [name='date_from'], [name='date_to']"
                        class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    <option value="">All Departments</option>
                    {% for dept in departments %}
                        <option value="{{ dept }}" {% if dept == department_filter %}selected{% endif %}>{{ dept }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                <input type="date" 
                       name="date_from" 
                       id="date_from"
                       value="{{ date_from }}"
                       hx-get="{% url 'pending_requests_table_htmx' %}"
                       hx-target="#requests-table"
                       hx-trigger="change"
                       hx-include="[name='search'], [name='department'], [name='date_to']"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
            
            <div>
                <label for="date_to" class="block text-sm font-medium text-gray-700">To Date</label>
                <input type="date" 
                       name="date_to" 
                       id="date_to"
                       value="{{ date_to }}"
                       hx-get="{% url 'pending_requests_table_htmx' %}"
                       hx-target="#requests-table"
                       hx-trigger="change"
                       hx-include="[name='search'], [name='department'], [name='date_from']"
                       class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
            </div>
        </div>
    </div>
</div>

<!-- Requests Table -->
<div class="bg-white shadow rounded-lg">
    <div id="requests-table">
        {% include 'approvals/partials/requests_table.html' %}
    </div>
</div>

<!-- Modal Container -->
<div id="modal-container"></div>

<!-- Bulk Approval Confirmation Modal -->
<div id="bulk-approval-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100">
                <svg class="h-6 w-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-2">Bulk Approve Requests</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500">
                    Are you sure you want to approve <span id="bulk-count">0</span> selected requests? 
                    This action cannot be undone.
                </p>
            </div>
            <div class="items-center px-4 py-3">
                <button id="confirm-bulk-approve"
                        class="px-4 py-2 bg-green-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-300 mr-2">
                    Approve All Selected
                </button>
                <button id="cancel-bulk-approve"
                        class="mt-3 px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Cancel
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let selectedRequests = new Set();
    
    // Handle individual checkbox changes
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('request-checkbox')) {
            const requestId = e.target.value;
            if (e.target.checked) {
                selectedRequests.add(requestId);
            } else {
                selectedRequests.delete(requestId);
            }
            updateBulkApproveButton();
        }
    });
    
    // Handle select all checkbox
    document.addEventListener('change', function(e) {
        if (e.target.id === 'select-all') {
            const checkboxes = document.querySelectorAll('.request-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = e.target.checked;
                const requestId = checkbox.value;
                if (e.target.checked) {
                    selectedRequests.add(requestId);
                } else {
                    selectedRequests.delete(requestId);
                }
            });
            updateBulkApproveButton();
        }
    });
    
    // Update bulk approve button state
    function updateBulkApproveButton() {
        const bulkBtn = document.getElementById('bulk-approve-btn');
        const countSpan = document.getElementById('selected-count');
        const count = selectedRequests.size;
        
        countSpan.textContent = count;
        bulkBtn.disabled = count === 0;
        
        if (count > 0) {
            bulkBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            bulkBtn.classList.add('opacity-50', 'cursor-not-allowed');
        }
    }
    
    // Handle bulk approve button click
    document.getElementById('bulk-approve-btn').addEventListener('click', function() {
        if (selectedRequests.size > 0) {
            document.getElementById('bulk-count').textContent = selectedRequests.size;
            document.getElementById('bulk-approval-modal').classList.remove('hidden');
        }
    });
    
    // Handle bulk approval confirmation
    document.getElementById('confirm-bulk-approve').addEventListener('click', function() {
        const formData = new FormData();
        selectedRequests.forEach(id => {
            formData.append('request_ids', id);
        });
        
        fetch('{% url "bulk_approve_requests_htmx" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'HX-Request': 'true'
            }
        })
        .then(response => response.text())
        .then(html => {
            document.getElementById('bulk-approval-modal').classList.add('hidden');
            // Show result message
            const messageDiv = document.createElement('div');
            messageDiv.innerHTML = html;
            document.body.appendChild(messageDiv);
            
            // Clear selections
            selectedRequests.clear();
            updateBulkApproveButton();
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('bulk-approval-modal').classList.add('hidden');
        });
    });
    
    // Handle bulk approval cancellation
    document.getElementById('cancel-bulk-approve').addEventListener('click', function() {
        document.getElementById('bulk-approval-modal').classList.add('hidden');
    });
    
    // Clear selections when table is refreshed
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        if (evt.target.id === 'requests-table') {
            selectedRequests.clear();
            updateBulkApproveButton();
        }
    });
});
</script>
{% endblock %}