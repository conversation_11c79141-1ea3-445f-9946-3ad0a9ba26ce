<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Navigation Submenu Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/admin_navigation.css">
    <style>
        /* Test-specific styles */
        .test-container {
            max-width: 300px;
            margin: 2rem auto;
            padding: 1rem;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }
        .test-result {
            margin-top: 1rem;
            padding: 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }
        .test-pass {
            background-color: #d1fae5;
            color: #065f46;
            border: 1px solid #10b981;
        }
        .test-fail {
            background-color: #fee2e2;
            color: #991b1b;
            border: 1px solid #ef4444;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="test-container">
        <h1 class="text-xl font-bold mb-4">Admin Navigation Submenu Test</h1>
        
        <!-- Test Navigation Section -->
        <div class="nav-section" data-nav-section="users">
            <a href="#" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200" data-nav-item="users">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                User Management
                <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </a>
            
            <!-- Submenu -->
            <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200" data-nav-item="users-list">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                    </svg>
                    View All Users
                </a>
                <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200" data-nav-item="users-add">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    Add User
                </a>
            </div>
        </div>
        
        <!-- Test Results -->
        <div id="test-results"></div>
        
        <!-- Test Controls -->
        <div class="mt-4">
            <button id="test-submenu" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                Test Submenu Toggle
            </button>
            <button id="run-all-tests" class="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 ml-2">
                Run All Tests
            </button>
        </div>
    </div>

    <script>
        // Simplified version of admin navigation for testing
        class SubmenuTest {
            constructor() {
                this.setupEventListeners();
                this.testResults = [];
            }

            setupEventListeners() {
                // Handle navigation link clicks
                document.addEventListener('click', (e) => {
                    const navLink = e.target.closest('.nav-link');
                    if (navLink) {
                        this.handleNavLinkClick(navLink, e);
                    }
                });

                // Test button
                document.getElementById('test-submenu').addEventListener('click', () => {
                    this.testSubmenuToggle();
                });

                document.getElementById('run-all-tests').addEventListener('click', () => {
                    this.runAllTests();
                });
            }

            handleNavLinkClick(navLink, event) {
                const navSection = navLink.closest('.nav-section');
                if (navSection) {
                    const submenu = navSection.querySelector('.nav-submenu');
                    if (submenu) {
                        event.preventDefault();
                        this.toggleSubmenu(navSection);
                    }
                }
            }

            toggleSubmenu(navSection) {
                const submenu = navSection.querySelector('.nav-submenu');
                const chevron = navSection.querySelector('.nav-link svg:last-child');
                
                if (!submenu) return;
                
                const isHidden = submenu.classList.contains('hidden');
                
                if (isHidden) {
                    // Show submenu
                    submenu.classList.remove('hidden');
                    chevron?.classList.add('rotate-180');
                } else {
                    // Hide submenu
                    submenu.classList.add('hidden');
                    chevron?.classList.remove('rotate-180');
                }
            }

            testSubmenuToggle() {
                const navSection = document.querySelector('.nav-section');
                const submenu = navSection.querySelector('.nav-submenu');
                const chevron = navSection.querySelector('.nav-link svg:last-child');
                
                // Test 1: Initial state (should be hidden)
                const initiallyHidden = submenu.classList.contains('hidden');
                this.addTestResult('Initial State', initiallyHidden, 'Submenu should start hidden');
                
                // Test 2: Toggle to show
                this.toggleSubmenu(navSection);
                const nowVisible = !submenu.classList.contains('hidden');
                const chevronRotated = chevron.classList.contains('rotate-180');
                this.addTestResult('Toggle Show', nowVisible && chevronRotated, 'Submenu should be visible with rotated chevron');
                
                // Test 3: Toggle to hide
                this.toggleSubmenu(navSection);
                const nowHidden = submenu.classList.contains('hidden');
                const chevronNormal = !chevron.classList.contains('rotate-180');
                this.addTestResult('Toggle Hide', nowHidden && chevronNormal, 'Submenu should be hidden with normal chevron');
                
                this.displayResults();
            }

            testCSSAnimations() {
                const submenu = document.querySelector('.nav-submenu');
                const computedStyle = window.getComputedStyle(submenu);
                
                // Test CSS transition
                const hasTransition = computedStyle.transition.includes('all');
                this.addTestResult('CSS Transitions', hasTransition, 'Submenu should have CSS transitions');
                
                // Test max-height behavior
                submenu.classList.remove('hidden');
                const visibleMaxHeight = parseInt(computedStyle.maxHeight);
                submenu.classList.add('hidden');
                const hiddenMaxHeight = parseInt(computedStyle.maxHeight);
                
                this.addTestResult('CSS Animation', visibleMaxHeight > hiddenMaxHeight, 'Max-height should change between states');
            }

            testEventHandling() {
                const navLink = document.querySelector('.nav-link');
                
                // Test click event
                let eventFired = false;
                const originalHandler = this.handleNavLinkClick;
                this.handleNavLinkClick = () => { eventFired = true; };
                
                navLink.click();
                this.addTestResult('Event Handling', eventFired, 'Click events should be handled');
                
                // Restore original handler
                this.handleNavLinkClick = originalHandler;
            }

            runAllTests() {
                this.testResults = [];
                this.testSubmenuToggle();
                this.testCSSAnimations();
                this.testEventHandling();
                this.displayResults();
            }

            addTestResult(testName, passed, description) {
                this.testResults.push({
                    name: testName,
                    passed: passed,
                    description: description
                });
            }

            displayResults() {
                const resultsContainer = document.getElementById('test-results');
                const passedTests = this.testResults.filter(t => t.passed).length;
                const totalTests = this.testResults.length;
                
                let html = `<h3 class="font-bold mt-4 mb-2">Test Results: ${passedTests}/${totalTests} Passed</h3>`;
                
                this.testResults.forEach(test => {
                    const cssClass = test.passed ? 'test-pass' : 'test-fail';
                    const icon = test.passed ? '✅' : '❌';
                    html += `
                        <div class="test-result ${cssClass}">
                            ${icon} <strong>${test.name}:</strong> ${test.description}
                        </div>
                    `;
                });
                
                resultsContainer.innerHTML = html;
            }
        }

        // Initialize test when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            window.submenuTest = new SubmenuTest();
        });
    </script>
</body>
</html>
