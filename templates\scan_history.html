{% extends 'base.html' %}

{% block page_title %}QR Scan History{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div class="flex items-center justify-between mb-4">
            <h1 class="text-2xl font-bold text-gray-900">QR Scan History</h1>
            <a href="{% url 'qr_scanner' %}" class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                Back to Scanner
            </a>
        </div>
        
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Search</label>
                <input type="text" 
                       name="search" 
                       value="{{ search_query }}"
                       placeholder="Search items, location, notes..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       hx-get="{% url 'scan_history' %}"
                       hx-trigger="keyup changed delay:500ms"
                       hx-target="#scan-history-table"
                       hx-include="[name='scan_type'], [name='date_from'], [name='date_to'], [name='user']">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Scan Type</label>
                <select name="scan_type" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        hx-get="{% url 'scan_history' %}"
                        hx-trigger="change"
                        hx-target="#scan-history-table"
                        hx-include="[name='search'], [name='date_from'], [name='date_to'], [name='user']">
                    <option value="">All Types</option>
                    {% for value, label in scan_type_choices %}
                    <option value="{{ value }}" {% if scan_type_filter == value %}selected{% endif %}>{{ label }}</option>
                    {% endfor %}
                </select>
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date From</label>
                <input type="date" 
                       name="date_from" 
                       value="{{ date_from }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       hx-get="{% url 'scan_history' %}"
                       hx-trigger="change"
                       hx-target="#scan-history-table"
                       hx-include="[name='search'], [name='scan_type'], [name='date_to'], [name='user']">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Date To</label>
                <input type="date" 
                       name="date_to" 
                       value="{{ date_to }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       hx-get="{% url 'scan_history' %}"
                       hx-trigger="change"
                       hx-target="#scan-history-table"
                       hx-include="[name='search'], [name='scan_type'], [name='date_from'], [name='user']">
            </div>
            
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">User</label>
                <input type="text" 
                       name="user" 
                       value="{{ user_filter }}"
                       placeholder="Username..."
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                       hx-get="{% url 'scan_history' %}"
                       hx-trigger="keyup changed delay:500ms"
                       hx-target="#scan-history-table"
                       hx-include="[name='search'], [name='scan_type'], [name='date_from'], [name='date_to']">
            </div>
        </div>
    </div>

    <!-- Scan History Table -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
        <div id="scan-history-table">
            {% include 'partials/scan_history_table.html' %}
        </div>
    </div>
</div>

<!-- CSRF Token -->
{% csrf_token %}
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-refresh every 30 seconds
    setInterval(() => {
        htmx.trigger('#scan-history-table', 'load');
    }, 30000);
</script>
{% endblock %}