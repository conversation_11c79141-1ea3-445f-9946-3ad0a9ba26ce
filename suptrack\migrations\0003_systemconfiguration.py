# Generated by Django 4.2.17 on 2025-07-29 04:18

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('suptrack', '0002_savedsearch'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=100, unique=True)),
                ('value', models.TextField()),
                ('setting_type', models.CharField(choices=[('string', 'String'), ('integer', 'Integer'), ('boolean', 'Boolean'), ('float', 'Float'), ('json', 'JSON')], default='string', max_length=20)),
                ('description', models.TextField(blank=True)),
                ('category', models.CharField(default='general', max_length=50)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'System Configuration',
                'verbose_name_plural': 'System Configurations',
                'ordering': ['category', 'key'],
                'indexes': [models.Index(fields=['key'], name='suptrack_sy_key_de5ed4_idx'), models.Index(fields=['category', 'is_active'], name='suptrack_sy_categor_531c2a_idx')],
            },
        ),
    ]
