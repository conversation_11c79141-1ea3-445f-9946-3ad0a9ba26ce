#!/usr/bin/env python
"""
Test script for inventory management URLs
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from django.urls import reverse
from suptrack.models import UserProfile, SupplyCategory, SupplyItem

def test_inventory_urls():
    """Test inventory management URLs"""
    
    print("🧪 Testing Inventory Management URLs")
    print("=" * 50)
    
    # Create test client
    client = Client()
    
    # Create admin user
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'first_name': 'Admin',
            'last_name': 'User',
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # Create user profile
    admin_profile, created = UserProfile.objects.get_or_create(
        user=admin_user,
        defaults={
            'role': 'admin',
            'department': 'Administration'
        }
    )
    
    # Login as admin
    client.login(username='admin_test', password='admin123')
    
    # Test URLs
    urls_to_test = [
        ('inventory_list', 'Inventory List'),
        ('add_supply_item', 'Add Supply Item'),
        ('low_stock_alerts', 'Low Stock Alerts'),
        ('inventory_transactions', 'Inventory Transactions'),
    ]
    
    print("\n1. Testing main inventory URLs...")
    for url_name, description in urls_to_test:
        try:
            url = reverse(url_name)
            response = client.get(url)
            status = "✓" if response.status_code == 200 else "✗"
            print(f"{status} {description}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"✗ {description}: Error - {str(e)}")
    
    # Test URLs that require an item ID
    print("\n2. Testing item-specific URLs...")
    
    # Create test category and item
    category, created = SupplyCategory.objects.get_or_create(
        name='Test Category',
        defaults={'description': 'Test category for URL testing'}
    )
    
    item, created = SupplyItem.objects.get_or_create(
        name='Test Item',
        defaults={
            'category': category,
            'description': 'Test item for URL testing',
            'unit_of_measure': 'pieces',
            'current_stock': 10,
            'minimum_stock': 5
        }
    )
    
    item_urls_to_test = [
        ('supply_item_detail', 'Supply Item Detail'),
        ('edit_supply_item', 'Edit Supply Item'),
        ('stock_adjustment', 'Stock Adjustment'),
        ('delete_confirmation_modal', 'Delete Confirmation Modal'),
    ]
    
    for url_name, description in item_urls_to_test:
        try:
            url = reverse(url_name, kwargs={'pk': item.pk})
            response = client.get(url)
            status = "✓" if response.status_code == 200 else "✗"
            print(f"{status} {description}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"✗ {description}: Error - {str(e)}")
    
    # Test HTMX endpoints
    print("\n3. Testing HTMX endpoints...")
    
    htmx_urls_to_test = [
        ('inventory_list_htmx', 'Inventory List HTMX'),
    ]
    
    for url_name, description in htmx_urls_to_test:
        try:
            url = reverse(url_name)
            response = client.get(url, HTTP_HX_REQUEST='true')
            status = "✓" if response.status_code == 200 else "✗"
            print(f"{status} {description}: {url} (Status: {response.status_code})")
        except Exception as e:
            print(f"✗ {description}: Error - {str(e)}")
    
    # Test dashboard widgets
    print("\n4. Testing dashboard integration...")
    
    try:
        dashboard_url = reverse('dashboard')
        response = client.get(dashboard_url)
        status = "✓" if response.status_code == 200 else "✗"
        print(f"{status} Dashboard: {dashboard_url} (Status: {response.status_code})")
        
        # Check if inventory management widget is present
        if b'Inventory Management' in response.content:
            print("✓ Inventory Management widget found in dashboard")
        else:
            print("✗ Inventory Management widget not found in dashboard")
            
    except Exception as e:
        print(f"✗ Dashboard: Error - {str(e)}")
    
    print("\n" + "=" * 50)
    print("✅ Inventory Management URL Test Complete!")

if __name__ == '__main__':
    test_inventory_urls()