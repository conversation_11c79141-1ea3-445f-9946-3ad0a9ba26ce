/**
 * Mobile Sidebar Test Suite
 * Comprehensive testing for mobile sidebar functionality
 */

class MobileSidebarTester {
    constructor() {
        this.testResults = [];
        this.originalViewport = {
            width: window.innerWidth,
            height: window.innerHeight
        };
    }

    /**
     * Run all mobile sidebar tests
     */
    async runAllTests() {
        console.log('🧪 Starting Mobile Sidebar Test Suite...');
        
        const tests = [
            this.testToggleButtonVisibility,
            this.testSidebarToggleFunctionality,
            this.testOverlayBehavior,
            this.testSwipeGestures,
            this.testAccessibilityFeatures,
            this.testResponsiveBreakpoints,
            this.testAnimationPerformance,
            this.testKeyboardNavigation,
            this.testTouchInteractions,
            this.testConsistentBehavior
        ];

        for (const test of tests) {
            try {
                await test.call(this);
            } catch (error) {
                this.logTestResult(test.name, false, error.message);
            }
        }

        this.generateTestReport();
    }

    /**
     * Test toggle button visibility on mobile
     */
    async testToggleButtonVisibility() {
        const testName = 'Toggle Button Visibility';
        
        // Simulate mobile viewport
        this.simulateMobileViewport();
        
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const isVisible = toggleBtn && window.getComputedStyle(toggleBtn).display !== 'none';
        const hasProperSize = toggleBtn && toggleBtn.offsetHeight >= 44 && toggleBtn.offsetWidth >= 44;
        
        this.logTestResult(testName, isVisible && hasProperSize, 
            isVisible ? (hasProperSize ? 'Toggle button is visible and properly sized' : 'Toggle button too small') : 'Toggle button not visible');
        
        this.restoreViewport();
    }

    /**
     * Test sidebar toggle functionality
     */
    async testSidebarToggleFunctionality() {
        const testName = 'Sidebar Toggle Functionality';
        
        this.simulateMobileViewport();
        
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const sidebar = document.querySelector('.custom-sidebar');
        
        if (!toggleBtn || !sidebar) {
            this.logTestResult(testName, false, 'Toggle button or sidebar not found');
            return;
        }

        // Test opening
        toggleBtn.click();
        await this.wait(350); // Wait for animation
        
        const isOpenAfterClick = sidebar.classList.contains('translate-x-0') || 
                                sidebar.classList.contains('open');
        
        // Test closing
        toggleBtn.click();
        await this.wait(350);
        
        const isClosedAfterSecondClick = sidebar.classList.contains('-translate-x-full') || 
                                        !sidebar.classList.contains('open');
        
        this.logTestResult(testName, isOpenAfterClick && isClosedAfterSecondClick, 
            `Open: ${isOpenAfterClick}, Close: ${isClosedAfterSecondClick}`);
        
        this.restoreViewport();
    }

    /**
     * Test overlay behavior
     */
    async testOverlayBehavior() {
        const testName = 'Overlay Behavior';
        
        this.simulateMobileViewport();
        
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const overlay = document.querySelector('.mobile-overlay-optimized');
        
        if (!toggleBtn || !overlay) {
            this.logTestResult(testName, false, 'Toggle button or overlay not found');
            return;
        }

        // Open sidebar
        toggleBtn.click();
        await this.wait(350);
        
        const overlayVisible = overlay.classList.contains('active') || 
                              window.getComputedStyle(overlay).opacity > 0;
        
        // Click overlay to close
        overlay.click();
        await this.wait(350);
        
        const overlayHidden = !overlay.classList.contains('active') || 
                             window.getComputedStyle(overlay).opacity == 0;
        
        this.logTestResult(testName, overlayVisible && overlayHidden, 
            `Overlay shows: ${overlayVisible}, Overlay hides: ${overlayHidden}`);
        
        this.restoreViewport();
    }

    /**
     * Test swipe gestures
     */
    async testSwipeGestures() {
        const testName = 'Swipe Gestures';
        
        this.simulateMobileViewport();
        
        // Simulate swipe right from left edge
        const swipeRightResult = this.simulateSwipe(10, 100, 100, 100);
        await this.wait(350);
        
        const sidebar = document.querySelector('.custom-sidebar');
        const openedBySwipe = sidebar && (sidebar.classList.contains('translate-x-0') || 
                                         sidebar.classList.contains('open'));
        
        // Simulate swipe left to close
        const swipeLeftResult = this.simulateSwipe(200, 50, 100, 100);
        await this.wait(350);
        
        const closedBySwipe = sidebar && (sidebar.classList.contains('-translate-x-full') || 
                                         !sidebar.classList.contains('open'));
        
        this.logTestResult(testName, openedBySwipe || closedBySwipe, 
            `Swipe gestures ${openedBySwipe || closedBySwipe ? 'working' : 'not working'}`);
        
        this.restoreViewport();
    }

    /**
     * Test accessibility features
     */
    async testAccessibilityFeatures() {
        const testName = 'Accessibility Features';
        
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const sidebar = document.querySelector('.custom-sidebar');
        
        const hasAriaLabel = toggleBtn && toggleBtn.getAttribute('aria-label');
        const hasAriaExpanded = toggleBtn && toggleBtn.hasAttribute('aria-expanded');
        const hasFocusManagement = sidebar && sidebar.hasAttribute('x-trap');
        
        this.logTestResult(testName, hasAriaLabel && hasAriaExpanded, 
            `ARIA label: ${!!hasAriaLabel}, ARIA expanded: ${!!hasAriaExpanded}, Focus trap: ${!!hasFocusManagement}`);
    }

    /**
     * Test responsive breakpoints
     */
    async testResponsiveBreakpoints() {
        const testName = 'Responsive Breakpoints';
        
        // Test mobile breakpoint (767px)
        this.setViewportSize(767, 800);
        await this.wait(100);
        
        const toggleBtnMobile = document.querySelector('.mobile-toggle-btn');
        const mobileVisible = toggleBtnMobile && window.getComputedStyle(toggleBtnMobile).display !== 'none';
        
        // Test desktop breakpoint (768px)
        this.setViewportSize(768, 800);
        await this.wait(100);
        
        const toggleBtnDesktop = document.querySelector('.mobile-toggle-btn');
        const desktopHidden = !toggleBtnDesktop || window.getComputedStyle(toggleBtnDesktop).display === 'none';
        
        this.logTestResult(testName, mobileVisible && desktopHidden, 
            `Mobile (767px): ${mobileVisible}, Desktop (768px): ${desktopHidden}`);
        
        this.restoreViewport();
    }

    /**
     * Test animation performance
     */
    async testAnimationPerformance() {
        const testName = 'Animation Performance';
        
        this.simulateMobileViewport();
        
        const sidebar = document.querySelector('.custom-sidebar');
        const hasTransform3d = sidebar && window.getComputedStyle(sidebar).transform !== 'none';
        const hasWillChange = sidebar && window.getComputedStyle(sidebar).willChange === 'transform';
        const hasBackfaceVisibility = sidebar && window.getComputedStyle(sidebar).backfaceVisibility === 'hidden';
        
        this.logTestResult(testName, hasTransform3d || hasWillChange, 
            `Transform3d: ${hasTransform3d}, Will-change: ${hasWillChange}, Backface: ${hasBackfaceVisibility}`);
        
        this.restoreViewport();
    }

    /**
     * Test keyboard navigation
     */
    async testKeyboardNavigation() {
        const testName = 'Keyboard Navigation';
        
        this.simulateMobileViewport();
        
        // Open sidebar
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        if (toggleBtn) {
            toggleBtn.click();
            await this.wait(350);
        }
        
        // Test Escape key
        const escapeEvent = new KeyboardEvent('keydown', { key: 'Escape' });
        document.dispatchEvent(escapeEvent);
        await this.wait(350);
        
        const sidebar = document.querySelector('.custom-sidebar');
        const closedByEscape = sidebar && (sidebar.classList.contains('-translate-x-full') || 
                                          !sidebar.classList.contains('open'));
        
        this.logTestResult(testName, closedByEscape, 
            `Escape key ${closedByEscape ? 'closes' : 'does not close'} sidebar`);
        
        this.restoreViewport();
    }

    /**
     * Test touch interactions
     */
    async testTouchInteractions() {
        const testName = 'Touch Interactions';
        
        this.simulateMobileViewport();
        
        const navLinks = document.querySelectorAll('.nav-link');
        const hasTouchSupport = navLinks.length > 0 && 'ontouchstart' in window;
        const hasProperTouchTargets = Array.from(navLinks).every(link => 
            link.offsetHeight >= 44 && link.offsetWidth >= 44);
        
        this.logTestResult(testName, hasTouchSupport && hasProperTouchTargets, 
            `Touch support: ${hasTouchSupport}, Proper targets: ${hasProperTouchTargets}`);
        
        this.restoreViewport();
    }

    /**
     * Test consistent behavior between admin and GSO
     */
    async testConsistentBehavior() {
        const testName = 'Consistent Behavior';
        
        const sidebar = document.querySelector('.custom-sidebar');
        const toggleBtn = document.querySelector('.mobile-toggle-btn');
        const overlay = document.querySelector('.mobile-overlay-optimized');
        
        const hasConsistentStructure = sidebar && toggleBtn && overlay;
        const hasConsistentClasses = sidebar && sidebar.classList.contains('custom-sidebar');
        
        this.logTestResult(testName, hasConsistentStructure && hasConsistentClasses, 
            `Structure: ${hasConsistentStructure}, Classes: ${hasConsistentClasses}`);
    }

    // Helper methods
    simulateMobileViewport() {
        this.setViewportSize(375, 667); // iPhone SE size
    }

    setViewportSize(width, height) {
        // Note: This is a simulation for testing purposes
        // In real testing, you'd use browser dev tools or testing frameworks
        Object.defineProperty(window, 'innerWidth', { value: width, writable: true });
        Object.defineProperty(window, 'innerHeight', { value: height, writable: true });
        window.dispatchEvent(new Event('resize'));
    }

    restoreViewport() {
        Object.defineProperty(window, 'innerWidth', { value: this.originalViewport.width, writable: true });
        Object.defineProperty(window, 'innerHeight', { value: this.originalViewport.height, writable: true });
        window.dispatchEvent(new Event('resize'));
    }

    simulateSwipe(startX, endX, startY, endY) {
        const touchStart = new TouchEvent('touchstart', {
            touches: [{ clientX: startX, clientY: startY }]
        });
        const touchEnd = new TouchEvent('touchend', {
            changedTouches: [{ clientX: endX, clientY: endY }]
        });
        
        document.dispatchEvent(touchStart);
        setTimeout(() => document.dispatchEvent(touchEnd), 100);
        
        return true;
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    logTestResult(testName, passed, details) {
        const result = {
            test: testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.testResults.push(result);
        
        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${details}`);
    }

    generateTestReport() {
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.passed).length;
        const failedTests = totalTests - passedTests;
        
        console.log('\n📊 Mobile Sidebar Test Report');
        console.log('================================');
        console.log(`Total Tests: ${totalTests}`);
        console.log(`Passed: ${passedTests}`);
        console.log(`Failed: ${failedTests}`);
        console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
        
        if (failedTests > 0) {
            console.log('\n❌ Failed Tests:');
            this.testResults.filter(r => !r.passed).forEach(result => {
                console.log(`  - ${result.test}: ${result.details}`);
            });
        }
        
        return {
            total: totalTests,
            passed: passedTests,
            failed: failedTests,
            successRate: (passedTests / totalTests) * 100,
            results: this.testResults
        };
    }
}

// Export for use in browser console or testing frameworks
window.MobileSidebarTester = MobileSidebarTester;

// Auto-run tests in development mode
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    document.addEventListener('DOMContentLoaded', () => {
        // Wait for all scripts to load
        setTimeout(() => {
            const tester = new MobileSidebarTester();
            // Uncomment the next line to auto-run tests
            // tester.runAllTests();
        }, 2000);
    });
}
