<div id="supply-item-{{ supply_item.id }}" class="bg-white border border-gray-200 rounded-lg p-4">
    <!-- Success message -->
    {% if success_message %}
        <div class="mb-4 bg-green-50 border border-green-200 rounded-md p-3">
            <div class="flex">
                <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">{{ success_message }}</p>
                </div>
            </div>
        </div>
    {% endif %}
    
    <!-- Item Header -->
    <div class="flex justify-between items-start mb-4">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">{{ supply_item.name }}</h3>
            <p class="text-sm text-gray-600">{{ supply_item.category.name }}</p>
        </div>
        
        <div class="flex space-x-2">
            <!-- Edit Button -->
            <button hx-get="{% url 'inline_edit_supply_item' supply_item.id %}"
                    hx-target="#supply-item-{{ supply_item.id }}"
                    hx-swap="outerHTML"
                    class="inline-flex items-center px-3 py-1 text-xs font-medium text-blue-700 bg-blue-100 rounded-full hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
                Edit
            </button>
            
            <!-- Quick Stock Adjustment -->
            <button hx-get="{% url 'quick_stock_adjustment' supply_item.id %}"
                    hx-target="#stock-adjustment-{{ supply_item.id }}"
                    hx-swap="innerHTML"
                    class="inline-flex items-center px-3 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-full hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Adjust Stock
            </button>
        </div>
    </div>
    
    <!-- Item Details -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div>
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wide">Current Stock</p>
            <div class="flex items-center mt-1">
                <p class="text-lg font-semibold text-gray-900">{{ supply_item.current_stock }}</p>
                <span class="ml-2 inline-flex px-2 py-1 text-xs font-semibold rounded-full
                    {% if supply_item.current_stock == 0 %}bg-red-100 text-red-800
                    {% elif supply_item.current_stock <= supply_item.minimum_stock %}bg-yellow-100 text-yellow-800
                    {% else %}bg-green-100 text-green-800{% endif %}">
                    {% if supply_item.current_stock == 0 %}Out of Stock
                    {% elif supply_item.current_stock <= supply_item.minimum_stock %}Low Stock
                    {% else %}Normal{% endif %}
                </span>
            </div>
        </div>
        
        <div>
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wide">Minimum Stock</p>
            <p class="text-lg font-semibold text-gray-900 mt-1">{{ supply_item.minimum_stock }}</p>
        </div>
        
        <div>
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wide">Unit</p>
            <p class="text-lg font-semibold text-gray-900 mt-1">{{ supply_item.unit_of_measure }}</p>
        </div>
        
        <div>
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wide">QR Code</p>
            <p class="text-sm text-gray-600 mt-1 font-mono">{{ supply_item.qr_code_data|truncatechars:12 }}</p>
        </div>
    </div>
    
    <!-- Description -->
    {% if supply_item.description %}
        <div class="mb-4">
            <p class="text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">Description</p>
            <p class="text-sm text-gray-700">{{ supply_item.description }}</p>
        </div>
    {% endif %}
    
    <!-- Stock Adjustment Area -->
    <div id="stock-adjustment-{{ supply_item.id }}" class="mt-4"></div>
    
    <!-- Last Updated -->
    <div class="text-xs text-gray-500 mt-4 pt-4 border-t border-gray-200">
        Last updated: {{ supply_item.updated_at|date:"M d, Y H:i" }}
    </div>
</div>
