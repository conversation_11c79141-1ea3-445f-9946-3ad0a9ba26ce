<div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" 
     x-data="{ open: true }" 
     x-show="open"
     x-transition:enter="ease-out duration-300"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    
    <div class="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white"
         x-transition:enter="ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         x-transition:leave="ease-in duration-200"
         x-transition:leave-start="opacity-100 transform scale-100"
         x-transition:leave-end="opacity-0 transform scale-95">
        
        <!-- Modal Header -->
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-xl leading-6 font-medium text-gray-900">Request Details</h3>
                <p class="text-sm text-gray-500">{{ request.request_number }}</p>
            </div>
            <button @click="open = false" 
                    class="text-gray-400 hover:text-gray-600 focus:outline-none">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Request Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Requester Information -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Requester Information</h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Name:</span>
                        <span class="text-gray-900">{{ request.requester.get_full_name|default:request.requester.username }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Email:</span>
                        <span class="text-gray-900">{{ request.requester.email }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Department:</span>
                        <span class="text-gray-900">{{ request.department }}</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-700">Request Date:</span>
                        <span class="text-gray-900">{{ request.request_date|date:"M d, Y g:i A" }}</span>
                    </div>
                </div>
            </div>
            
            <!-- Request Status -->
            <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Request Status</h4>
                <div class="space-y-2 text-sm">
                    <div>
                        <span class="font-medium text-gray-700">Status:</span>
                        {% if request.status == 'pending' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-2">
                                Pending
                            </span>
                        {% elif request.status == 'approved' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
                                Approved
                            </span>
                        {% elif request.status == 'rejected' %}
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 ml-2">
                                Rejected
                            </span>
                        {% endif %}
                    </div>
                    {% if request.approved_by %}
                        <div>
                            <span class="font-medium text-gray-700">Processed by:</span>
                            <span class="text-gray-900">{{ request.approved_by.get_full_name|default:request.approved_by.username }}</span>
                        </div>
                        <div>
                            <span class="font-medium text-gray-700">Processed on:</span>
                            <span class="text-gray-900">{{ request.approved_date|date:"M d, Y g:i A" }}</span>
                        </div>
                    {% endif %}
                    {% if request.rejection_reason %}
                        <div>
                            <span class="font-medium text-gray-700">Rejection Reason:</span>
                            <div class="mt-1 p-2 bg-red-50 border border-red-200 rounded text-red-800 text-xs">
                                {{ request.rejection_reason }}
                            </div>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Request Notes -->
        {% if request.notes %}
            <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2">Request Notes</h4>
                <div class="bg-blue-50 border border-blue-200 rounded p-3 text-sm text-blue-800">
                    {{ request.notes }}
                </div>
            </div>
        {% endif %}
        
        <!-- Requested Items -->
        <div class="mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Requested Items</h4>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Item
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Category
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Quantity Requested
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Unit
                            </th>
                            {% if request.status != 'pending' %}
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                    Quantity Approved
                                </th>
                            {% endif %}
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for item in request.items.all %}
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900">{{ item.supply_item.name }}</div>
                                    {% if item.supply_item.description %}
                                        <div class="text-sm text-gray-500">{{ item.supply_item.description|truncatechars:50 }}</div>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ item.supply_item.category.name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ item.quantity_requested }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ item.supply_item.unit_of_measure }}
                                </td>
                                {% if request.status != 'pending' %}
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {% if request.status == 'approved' %}
                                            <span class="text-green-600 font-medium">{{ item.quantity_approved }}</span>
                                        {% elif request.status == 'rejected' %}
                                            <span class="text-red-600">-</span>
                                        {% endif %}
                                    </td>
                                {% endif %}
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Summary -->
        <div class="bg-gray-50 p-4 rounded-lg mb-6">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Summary</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="font-medium text-gray-700">Total Items:</span>
                    <span class="text-gray-900">{{ request.total_items }}</span>
                </div>
                <div>
                    <span class="font-medium text-gray-700">Total Quantity:</span>
                    <span class="text-gray-900">{{ request.total_quantity }}</span>
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        {% if request.status == 'pending' %}
            <div class="flex items-center justify-end space-x-3">
                <button @click="open = false"
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Close
                </button>
                
                <button hx-get="{% url 'rejection_modal_htmx' request.id %}"
                        hx-target="#modal-container"
                        hx-swap="innerHTML"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Reject Request
                </button>
                
                <button hx-post="{% url 'approve_request_htmx' request.id %}"
                        hx-target="#request-row-{{ request.id }}"
                        hx-swap="outerHTML"
                        hx-confirm="Are you sure you want to approve this request?"
                        hx-on::after-request="if(event.detail.successful) { document.getElementById('modal-container').innerHTML = ''; }"
                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Approve Request
                </button>
            </div>
        {% else %}
            <div class="flex items-center justify-end">
                <button @click="open = false"
                        class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Close
                </button>
            </div>
        {% endif %}
    </div>
</div>

<script>
// Handle escape key to close modal
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        document.getElementById('modal-container').innerHTML = '';
    }
});
</script>