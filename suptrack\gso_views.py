"""
GSO-specific views for the Smart Supply Management System.
These views provide GSO staff with dedicated interfaces for their operations.
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse
from django.urls import reverse
from django.db.models import Q, Count, F
from django.utils import timezone
from django.core.paginator import Paginator

from .models import SupplyRequest, SupplyItem, QRScanLog, InventoryTransaction
from .decorators import role_required
from .auth_views import get_gso_dashboard_stats, get_gso_recent_activities


@login_required
@role_required(['gso_staff'])
def gso_dashboard_main(request):
    """Main GSO dashboard with comprehensive overview"""
    context = {
        'user_profile': request.user.userprofile,
        'stats': get_gso_dashboard_stats(),
        'recent_activities': get_gso_recent_activities(),
        'page_title': 'GSO Dashboard',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'Dashboard', 'url': None}
        ]
    }
    return render(request, 'gso/dashboard.html', context)


@login_required
@role_required(['gso_staff'])
def gso_inventory_overview(request):
    """GSO inventory overview with quick stats and actions"""
    # Get inventory statistics
    total_items = SupplyItem.objects.count()
    low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    out_of_stock_items = SupplyItem.objects.filter(current_stock=0).count()
    
    # Recent transactions
    recent_transactions = InventoryTransaction.objects.select_related(
        'supply_item', 'performed_by'
    ).order_by('-transaction_date')[:10]
    
    # Items needing attention
    attention_items = SupplyItem.objects.filter(
        Q(current_stock__lte=F('minimum_stock')) | Q(current_stock=0)
    ).select_related('category').order_by('current_stock')[:10]
    
    context = {
        'total_items': total_items,
        'low_stock_items': low_stock_items,
        'out_of_stock_items': out_of_stock_items,
        'recent_transactions': recent_transactions,
        'attention_items': attention_items,
        'page_title': 'Inventory Overview',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'Inventory', 'url': None}
        ]
    }
    return render(request, 'gso/inventory_overview.html', context)


@login_required
@role_required(['gso_staff'])
def gso_approvals_overview(request):
    """GSO approvals overview with pending requests and quick actions"""
    # Get pending requests sorted by request date
    pending_requests = SupplyRequest.objects.filter(
        status='pending'
    ).select_related('requester', 'requester__userprofile').prefetch_related(
        'items__supply_item'
    ).order_by('-request_date')
    
    # Pagination
    paginator = Paginator(pending_requests, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics
    total_pending = pending_requests.count()
    urgent_requests = pending_requests.filter(
        request_date__lte=timezone.now() - timezone.timedelta(days=3)
    ).count()
    
    context = {
        'page_obj': page_obj,
        'total_pending': total_pending,
        'urgent_requests': urgent_requests,
        'page_title': 'Approvals Overview',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'Approvals', 'url': None}
        ]
    }
    return render(request, 'gso/approvals_overview.html', context)


@login_required
@role_required(['gso_staff'])
def gso_requests_overview(request):
    """GSO requests overview showing all requests with filtering"""
    # Get all requests with filtering
    status_filter = request.GET.get('status', '')
    search_query = request.GET.get('search', '')

    requests = SupplyRequest.objects.select_related(
        'requester', 'requester__userprofile'
    ).prefetch_related('items__supply_item')

    # Apply filters
    if status_filter:
        requests = requests.filter(status=status_filter)
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query) |
            Q(requester__username__icontains=search_query)
        )
    
    requests = requests.order_by('-request_date')
    
    # Pagination
    paginator = Paginator(requests, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # Statistics for filters
    status_counts = SupplyRequest.objects.values('status').annotate(count=Count('id'))

    context = {
        'page_obj': page_obj,
        'status_filter': status_filter,
        'search_query': search_query,
        'status_counts': {item['status']: item['count'] for item in status_counts},
        'page_title': 'All Requests',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'Requests', 'url': None}
        ]
    }
    return render(request, 'gso/requests_overview.html', context)


@login_required
@role_required(['gso_staff'])
def gso_qr_overview(request):
    """GSO QR scanner overview with recent scans and quick actions"""
    # Recent scans
    recent_scans = QRScanLog.objects.select_related(
        'supply_item', 'scanned_by'
    ).order_by('-scan_datetime')[:15]

    # Scan statistics
    today = timezone.now().date()
    today_scans = QRScanLog.objects.filter(scan_datetime__date=today).count()
    week_scans = QRScanLog.objects.filter(
        scan_datetime__gte=today - timezone.timedelta(days=7)
    ).count()
    
    # Items without QR codes
    items_without_qr = SupplyItem.objects.filter(
        Q(qr_code='') | Q(qr_code__isnull=True)
    ).count()
    
    context = {
        'recent_scans': recent_scans,
        'today_scans': today_scans,
        'week_scans': week_scans,
        'items_without_qr': items_without_qr,
        'page_title': 'QR Scanner Overview',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'QR Scanner', 'url': None}
        ]
    }
    return render(request, 'gso/qr_overview.html', context)


@login_required
@role_required(['gso_staff'])
def gso_reports_overview(request):
    """GSO reports overview with available reports and quick stats"""
    # Generate quick statistics for reports
    today = timezone.now().date()
    this_month = today.replace(day=1)
    
    # Request statistics
    total_requests = SupplyRequest.objects.count()
    pending_requests = SupplyRequest.objects.filter(status='pending').count()
    approved_requests = SupplyRequest.objects.filter(status='approved').count()
    month_requests = SupplyRequest.objects.filter(request_date__gte=this_month).count()
    
    # Inventory statistics
    total_items = SupplyItem.objects.count()
    low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    total_value = sum(item.current_stock * (item.unit_cost or 0) for item in SupplyItem.objects.all())
    
    context = {
        'request_stats': {
            'total': total_requests,
            'pending': pending_requests,
            'approved': approved_requests,
            'this_month': month_requests,
        },
        'inventory_stats': {
            'total_items': total_items,
            'low_stock': low_stock_items,
            'total_value': total_value,
        },
        'page_title': 'Reports Overview',
        'breadcrumbs': [
            {'name': 'GSO', 'url': reverse('gso_dashboard_main')},
            {'name': 'Reports', 'url': None}
        ]
    }
    return render(request, 'gso/reports_overview.html', context)


@login_required
@role_required(['gso_staff'])
def gso_quick_actions(request):
    """HTMX endpoint for GSO quick actions widget"""
    pending_count = SupplyRequest.objects.filter(status='pending').count()
    low_stock_count = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock')).count()
    
    context = {
        'pending_count': pending_count,
        'low_stock_count': low_stock_count,
    }
    return render(request, 'gso/partials/quick_actions.html', context)
