"""
Tests for QR code utilities
"""
from django.test import TestCase
from django.core.files.uploadedfile import SimpleUploadedFile
from django.conf import settings
from suptrack.models import SupplyCategory, SupplyItem
from suptrack.qr_utils import (
    QRCodeGenerator, generate_qr_code_for_item, 
    regenerate_qr_code_for_item, batch_generate_qr_codes_for_items
)
from PIL import Image
import qrcode
import os
import tempfile
import uuid


class QRCodeGeneratorTest(TestCase):
    """Test QRCodeGenerator utility class"""
    
    def setUp(self):
        """Set up test data"""
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category for QR testing'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
        
        self.generator = QRCodeGenerator()
    
    def test_qr_code_data_generation(self):
        """Test QR code data generation"""
        # Item without QR code data
        item_without_qr = SupplyItem.objects.create(
            name='Item Without QR',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=5,
            minimum_stock=2,
            qr_code_data=''  # Empty QR code data
        )
        
        qr_data = self.generator.generate_qr_code_data(item_without_qr)
        
        # Should generate and save QR code data
        self.assertIsNotNone(qr_data)
        self.assertTrue(len(qr_data) > 0)
        
        # Refresh from database
        item_without_qr.refresh_from_db()
        self.assertEqual(item_without_qr.qr_code_data, qr_data)
        
        # Should be a valid UUID
        try:
            uuid.UUID(qr_data)
        except ValueError:
            self.fail("Generated QR code data is not a valid UUID")
    
    def test_qr_code_data_preservation(self):
        """Test that existing QR code data is preserved"""
        original_qr_data = self.supply_item.qr_code_data
        
        # Call generate_qr_code_data again
        qr_data = self.generator.generate_qr_code_data(self.supply_item)
        
        # Should return the same data
        self.assertEqual(qr_data, original_qr_data)
    
    def test_qr_code_image_generation(self):
        """Test QR code image generation"""
        qr_img = self.generator.generate_qr_code_image(self.supply_item, include_label=False)

        # Should be a QR code image (either PIL Image or qrcode.image.pil.PilImage)
        self.assertTrue(hasattr(qr_img, 'width'))
        self.assertTrue(hasattr(qr_img, 'height'))

        # Should have reasonable dimensions
        self.assertGreater(qr_img.width, 0)
        self.assertGreater(qr_img.height, 0)
    
    def test_qr_code_image_with_label(self):
        """Test QR code image generation with label"""
        qr_img = self.generator.generate_qr_code_image(self.supply_item, include_label=True)

        # Should be a QR code image (either PIL Image or qrcode.image.pil.PilImage)
        self.assertTrue(hasattr(qr_img, 'width'))
        self.assertTrue(hasattr(qr_img, 'height'))

        # With label, image should be taller than without label
        qr_img_no_label = self.generator.generate_qr_code_image(self.supply_item, include_label=False)
        self.assertGreater(qr_img.height, qr_img_no_label.height)
    
    def test_save_qr_code_to_supply_item(self):
        """Test saving QR code image to supply item"""
        # Ensure item doesn't have QR code initially
        self.supply_item.qr_code = None
        self.supply_item.save()
        
        qr_url = self.generator.save_qr_code_to_supply_item(self.supply_item)
        
        # Should return a URL
        self.assertIsNotNone(qr_url)
        self.assertTrue(qr_url.startswith('/media/'))
        
        # Refresh from database
        self.supply_item.refresh_from_db()
        
        # Should have QR code file
        self.assertTrue(self.supply_item.qr_code)
        self.assertTrue(self.supply_item.qr_code.name.startswith('qr_codes/'))
    
    def test_regenerate_qr_code(self):
        """Test QR code regeneration"""
        # First generate a QR code
        original_url = self.generator.save_qr_code_to_supply_item(self.supply_item)
        original_qr_data = self.supply_item.qr_code_data
        
        # Regenerate QR code
        new_url = self.generator.regenerate_qr_code(self.supply_item)
        
        # Should have new URL
        self.assertIsNotNone(new_url)
        
        # Refresh from database
        self.supply_item.refresh_from_db()
        
        # Should have new QR code data
        self.assertNotEqual(self.supply_item.qr_code_data, original_qr_data)
        
        # Should have new QR code file
        self.assertTrue(self.supply_item.qr_code)
    
    def test_batch_generate_qr_codes(self):
        """Test batch QR code generation"""
        # Create multiple items without QR codes
        items = []
        for i in range(3):
            item = SupplyItem.objects.create(
                name=f'Batch Item {i+1}',
                category=self.category,
                unit_of_measure='pieces',
                current_stock=10,
                minimum_stock=5
            )
            item.qr_code = None
            item.save()
            items.append(item)
        
        results = self.generator.batch_generate_qr_codes(items)
        
        # Should return results for all items
        self.assertEqual(len(results), 3)
        
        # All should be successful
        for result in results:
            self.assertTrue(result['success'])
            self.assertIsNotNone(result['qr_url'])
        
        # All items should have QR codes
        for item in items:
            item.refresh_from_db()
            self.assertTrue(item.qr_code)


class QRUtilityFunctionsTest(TestCase):
    """Test QR utility convenience functions"""
    
    def setUp(self):
        """Set up test data"""
        self.category = SupplyCategory.objects.create(
            name='Test Category',
            description='Test category'
        )
        
        self.supply_item = SupplyItem.objects.create(
            name='Test Item',
            category=self.category,
            unit_of_measure='pieces',
            current_stock=10,
            minimum_stock=5
        )
    
    def test_generate_qr_code_for_item(self):
        """Test generate_qr_code_for_item convenience function"""
        # Clear existing QR code
        self.supply_item.qr_code = None
        self.supply_item.save()
        
        qr_url = generate_qr_code_for_item(self.supply_item)
        
        self.assertIsNotNone(qr_url)
        self.assertTrue(qr_url.startswith('/media/'))
        
        # Refresh and check
        self.supply_item.refresh_from_db()
        self.assertTrue(self.supply_item.qr_code)
    
    def test_regenerate_qr_code_for_item(self):
        """Test regenerate_qr_code_for_item convenience function"""
        # First generate a QR code
        generate_qr_code_for_item(self.supply_item)
        original_qr_data = self.supply_item.qr_code_data
        
        # Regenerate
        new_url = regenerate_qr_code_for_item(self.supply_item)
        
        self.assertIsNotNone(new_url)
        
        # Refresh and check
        self.supply_item.refresh_from_db()
        self.assertNotEqual(self.supply_item.qr_code_data, original_qr_data)
    
    def test_batch_generate_qr_codes_for_items(self):
        """Test batch_generate_qr_codes_for_items convenience function"""
        # Create multiple items
        items = []
        for i in range(2):
            item = SupplyItem.objects.create(
                name=f'Batch Item {i+1}',
                category=self.category,
                unit_of_measure='pieces',
                current_stock=10,
                minimum_stock=5
            )
            items.append(item)
        
        results = batch_generate_qr_codes_for_items(items)
        
        self.assertEqual(len(results), 2)
        for result in results:
            self.assertTrue(result['success'])
    
    def tearDown(self):
        """Clean up test files"""
        # Clean up any QR code files created during tests
        for item in SupplyItem.objects.all():
            if item.qr_code:
                try:
                    item.qr_code.delete()
                except:
                    pass
