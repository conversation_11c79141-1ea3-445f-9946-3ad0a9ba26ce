<!-- Stock Statistics -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-blue-600">{{ stock_stats.total_items }}</p>
            <p class="text-sm text-gray-600">Total Items</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ stock_stats.normal_stock_items }}</p>
            <p class="text-sm text-gray-600">Normal Stock</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-yellow-600">{{ stock_stats.low_stock_items }}</p>
            <p class="text-sm text-gray-600">Low Stock</p>
        </div>
    </div>
    <div class="bg-white rounded-lg shadow p-4">
        <div class="text-center">
            <p class="text-2xl font-bold text-red-600">{{ stock_stats.out_of_stock_items }}</p>
            <p class="text-sm text-gray-600">Out of Stock</p>
        </div>
    </div>
</div>

<!-- Category Breakdown and Reorder Recommendations -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Category Breakdown -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Stock by Category</h3>
        <div class="space-y-3">
            {% for category in category_breakdown %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ category.name }}</p>
                    <p class="text-sm text-gray-600">{{ category.total_items }} items, {{ category.total_stock|default:0 }} total stock</p>
                </div>
                <div class="flex space-x-2 text-xs">
                    {% if category.low_stock > 0 %}
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded">{{ category.low_stock }} low</span>
                    {% endif %}
                    {% if category.out_of_stock > 0 %}
                        <span class="px-2 py-1 bg-red-100 text-red-800 rounded">{{ category.out_of_stock }} out</span>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No category data available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Reorder Recommendations -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Reorder Recommendations</h3>
        <div class="space-y-3 max-h-64 overflow-y-auto">
            {% for item in reorder_recommendations %}
            <div class="flex justify-between items-center p-3 border border-red-200 bg-red-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ item.name }}</p>
                    <p class="text-sm text-red-600">Current: {{ item.current_stock }}, Min: {{ item.minimum_stock }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-red-800">Order: {{ item.recommended_order }}</p>
                    <p class="text-xs text-red-600">Shortage: {{ item.shortage }}</p>
                </div>
            </div>
            {% empty %}
            <div class="text-center py-8">
                <svg class="w-12 h-12 text-green-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <p class="text-green-600 font-medium">All items are adequately stocked!</p>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Usage Trends and Recent Transactions -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
    <!-- Usage Trends -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Most Active Items</h3>
        <div class="space-y-3">
            {% for item in usage_trends %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ item.supply_item__name }}</p>
                    <p class="text-sm text-gray-600">{{ item.transaction_count }} transactions</p>
                </div>
                <div class="text-right text-xs">
                    <p class="text-green-600">In: {{ item.total_in|default:0 }}</p>
                    <p class="text-red-600">Out: {{ item.total_out|default:0 }}</p>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No transaction data available</p>
            {% endfor %}
        </div>
    </div>

    <!-- Recent Transactions -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Transactions</h3>
        <div class="space-y-3 max-h-64 overflow-y-auto">
            {% for transaction in recent_transactions %}
            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-900">{{ transaction.supply_item.name }}</p>
                    <p class="text-sm text-gray-600">{{ transaction.transaction_date|date:"M d, H:i" }}</p>
                </div>
                <div class="text-right">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        {% if transaction.transaction_type == 'in' %}bg-green-100 text-green-800
                        {% elif transaction.transaction_type == 'out' %}bg-red-100 text-red-800
                        {% else %}bg-blue-100 text-blue-800{% endif %}">
                        {{ transaction.get_transaction_type_display }} {{ transaction.quantity }}
                    </span>
                </div>
            </div>
            {% empty %}
            <p class="text-gray-500 text-center py-4">No recent transactions</p>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Detailed Inventory List -->
<div class="bg-white rounded-lg shadow-md">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-semibold text-gray-900">Inventory Details</h3>
    </div>
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Stock</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Minimum Stock</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for item in page_obj %}
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <a href="{% url 'supply_item_detail' item.id %}" class="text-blue-600 hover:text-blue-800 font-medium">
                            {{ item.name }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ item.category.name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900">{{ item.current_stock }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ item.minimum_stock }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">{{ item.unit_of_measure }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        {% if item.current_stock == 0 %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-red-100 text-red-800">
                                Out of Stock
                            </span>
                        {% elif item.current_stock <= item.minimum_stock %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                Low Stock
                            </span>
                        {% else %}
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                Normal
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <a href="{% url 'supply_item_detail' item.id %}" class="text-blue-600 hover:text-blue-900">View</a>
                        <span class="text-gray-300 mx-1">|</span>
                        <a href="{% url 'stock_adjustment' item.id %}" class="text-green-600 hover:text-green-900">Adjust</a>
                    </td>
                </tr>
                {% empty %}
                <tr>
                    <td colspan="7" class="px-6 py-4 text-center text-gray-500">
                        No items found matching the current filters.
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
    <div class="px-6 py-4 border-t border-gray-200">
        <div class="flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
            </div>
            <div class="flex space-x-1">
                {% if page_obj.has_previous %}
                    <a href="?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Previous</a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <a href="?page={{ num }}&{{ request.GET.urlencode }}" 
                           class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">{{ num }}</a>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}" 
                       class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">Next</a>
                {% endif %}
            </div>
        </div>
    </div>
    {% endif %}
</div>
