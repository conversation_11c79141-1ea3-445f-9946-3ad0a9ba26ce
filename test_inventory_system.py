#!/usr/bin/env python
"""
Test script for inventory management system functionality
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.contrib.auth.models import User
from suptrack.models import UserProfile, SupplyCategory, SupplyItem, InventoryTransaction

def test_inventory_system():
    """Test the inventory management system components"""
    
    print("🧪 Testing Inventory Management System")
    print("=" * 50)
    
    # Test 1: Create test data
    print("\n1. Creating test data...")
    
    # Create admin user if not exists
    admin_user, created = User.objects.get_or_create(
        username='admin_test',
        defaults={
            'first_name': 'Admin',
            'last_name': 'User',
            'email': '<EMAIL>',
            'is_staff': True,
            'is_superuser': True
        }
    )
    if created:
        admin_user.set_password('admin123')
        admin_user.save()
    
    # Create user profile
    admin_profile, created = UserProfile.objects.get_or_create(
        user=admin_user,
        defaults={
            'role': 'admin',
            'department': 'Administration'
        }
    )
    
    # Create test category
    category, created = SupplyCategory.objects.get_or_create(
        name='Office Supplies',
        defaults={'description': 'General office supplies and materials'}
    )
    
    print(f"✓ Admin user: {admin_user.username}")
    print(f"✓ Category: {category.name}")
    
    # Test 2: Create supply items
    print("\n2. Creating supply items...")
    
    items_data = [
        {
            'name': 'A4 Paper',
            'description': 'White A4 printing paper',
            'unit_of_measure': 'reams',
            'current_stock': 50,
            'minimum_stock': 10
        },
        {
            'name': 'Blue Pens',
            'description': 'Blue ballpoint pens',
            'unit_of_measure': 'pieces',
            'current_stock': 5,  # Low stock
            'minimum_stock': 20
        },
        {
            'name': 'Stapler',
            'description': 'Heavy duty stapler',
            'unit_of_measure': 'pieces',
            'current_stock': 0,  # Out of stock
            'minimum_stock': 5
        }
    ]
    
    created_items = []
    for item_data in items_data:
        item, created = SupplyItem.objects.get_or_create(
            name=item_data['name'],
            defaults={
                'category': category,
                'description': item_data['description'],
                'unit_of_measure': item_data['unit_of_measure'],
                'current_stock': item_data['current_stock'],
                'minimum_stock': item_data['minimum_stock']
            }
        )
        created_items.append(item)
        print(f"✓ Item: {item.name} ({item.current_stock} {item.unit_of_measure})")
    
    # Test 3: Test stock level properties
    print("\n3. Testing stock level indicators...")
    
    for item in created_items:
        status = "Normal"
        if item.is_out_of_stock:
            status = "Out of Stock"
        elif item.is_low_stock:
            status = "Low Stock"
        
        print(f"✓ {item.name}: {status}")
    
    # Test 4: Create inventory transactions
    print("\n4. Creating inventory transactions...")
    
    # Stock in transaction for A4 Paper
    a4_paper = created_items[0]
    transaction = InventoryTransaction.objects.create(
        supply_item=a4_paper,
        transaction_type='in',
        quantity=25,
        previous_stock=a4_paper.current_stock,
        new_stock=a4_paper.current_stock + 25,
        performed_by=admin_user,
        notes='Initial stock replenishment'
    )
    
    # Update the item stock
    a4_paper.current_stock += 25
    a4_paper.save()
    
    print(f"✓ Stock In: {transaction.supply_item.name} +{transaction.quantity}")
    
    # Stock out transaction for Blue Pens
    blue_pens = created_items[1]
    transaction = InventoryTransaction.objects.create(
        supply_item=blue_pens,
        transaction_type='out',
        quantity=2,
        previous_stock=blue_pens.current_stock,
        new_stock=max(0, blue_pens.current_stock - 2),
        performed_by=admin_user,
        notes='Issued to department'
    )
    
    # Update the item stock
    blue_pens.current_stock = max(0, blue_pens.current_stock - 2)
    blue_pens.save()
    
    print(f"✓ Stock Out: {transaction.supply_item.name} -{transaction.quantity}")
    
    # Test 5: Query low stock items
    print("\n5. Testing low stock queries...")
    
    from django.db.models import F
    
    low_stock_items = SupplyItem.objects.filter(current_stock__lte=F('minimum_stock'))
    out_of_stock_items = SupplyItem.objects.filter(current_stock=0)
    
    print(f"✓ Low stock items: {low_stock_items.count()}")
    print(f"✓ Out of stock items: {out_of_stock_items.count()}")
    
    for item in low_stock_items:
        print(f"  - {item.name}: {item.current_stock}/{item.minimum_stock}")
    
    # Test 6: Test transaction history
    print("\n6. Testing transaction history...")
    
    total_transactions = InventoryTransaction.objects.count()
    recent_transactions = InventoryTransaction.objects.order_by('-transaction_date')[:5]
    
    print(f"✓ Total transactions: {total_transactions}")
    print("✓ Recent transactions:")
    for trans in recent_transactions:
        print(f"  - {trans.supply_item.name}: {trans.get_transaction_type_display()} {trans.quantity}")
    
    # Test 7: Test QR code data generation
    print("\n7. Testing QR code data...")
    
    for item in created_items:
        if item.qr_code_data:
            print(f"✓ {item.name}: QR Code = {item.qr_code_data}")
        else:
            print(f"✗ {item.name}: No QR Code data")
    
    print("\n" + "=" * 50)
    print("✅ Inventory Management System Test Complete!")
    
    # Summary
    print(f"\nSummary:")
    print(f"- Total Supply Items: {SupplyItem.objects.count()}")
    print(f"- Total Categories: {SupplyCategory.objects.count()}")
    print(f"- Total Transactions: {InventoryTransaction.objects.count()}")
    print(f"- Low Stock Alerts: {low_stock_items.count()}")
    print(f"- Out of Stock Items: {out_of_stock_items.count()}")

if __name__ == '__main__':
    test_inventory_system()