from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.db import transaction
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.template.loader import render_to_string
from .models import SupplyRequest, RequestItem, SupplyItem
from .forms import SupplyRequestForm, RequestItemInlineFormSet
from .decorators import role_required


@login_required
@role_required(['department_user', 'admin'])
def create_request(request):
    """Create a new supply request with items"""
    if request.method == 'POST':
        form = SupplyRequestForm(request.POST, user=request.user)
        formset = RequestItemInlineFormSet(request.POST)
        
        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    # Create the supply request
                    supply_request = form.save(commit=False)
                    supply_request.requester = request.user
                    supply_request.save()
                    
                    # Save the request items
                    formset.instance = supply_request
                    formset.save()
                    
                    messages.success(request, f'Supply request {supply_request.request_number} created successfully!')
                    
                    # Return HTMX response or redirect
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">'
                            f'Request {supply_request.request_number} created successfully! '
                            f'<a href="{supply_request.get_absolute_url()}" class="underline">View request</a>'
                            f'</div>'
                        )
                    
                    return redirect('request_detail', pk=supply_request.pk)
                    
            except Exception as e:
                messages.error(request, f'Error creating request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">'
                        f'Error creating request: {str(e)}'
                        f'</div>'
                    )
        else:
            # Handle form errors
            if request.headers.get('HX-Request'):
                context = {
                    'form': form,
                    'formset': formset,
                    'supply_items': SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
                }
                return render(request, 'requests/partials/request_form.html', context)
    else:
        form = SupplyRequestForm(user=request.user)
        formset = RequestItemInlineFormSet()
    
    context = {
        'form': form,
        'formset': formset,
        'supply_items': SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
    }
    
    return render(request, 'requests/create_request.html', context)


@login_required
def request_list(request):
    """List supply requests with filtering and search"""
    requests = SupplyRequest.objects.select_related('requester', 'approved_by').prefetch_related('items__supply_item')
    
    # Filter by user role
    if hasattr(request.user, 'userprofile'):
        if request.user.userprofile.role == 'department_user':
            requests = requests.filter(requester=request.user)
    
    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        requests = requests.filter(
            Q(request_number__icontains=search_query) |
            Q(requester__username__icontains=search_query) |
            Q(requester__first_name__icontains=search_query) |
            Q(requester__last_name__icontains=search_query) |
            Q(department__icontains=search_query)
        )
    
    # Status filtering
    status_filter = request.GET.get('status', '')
    if status_filter:
        requests = requests.filter(status=status_filter)
    
    # Date filtering
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    if date_from:
        requests = requests.filter(request_date__date__gte=date_from)
    if date_to:
        requests = requests.filter(request_date__date__lte=date_to)
    
    # Order by most recent first
    requests = requests.order_by('-request_date')
    
    # Pagination
    paginator = Paginator(requests, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # HTMX partial update
    if request.headers.get('HX-Request'):
        return render(request, 'requests/partials/request_list.html', {
            'page_obj': page_obj,
            'search_query': search_query,
            'status_filter': status_filter,
            'date_from': date_from,
            'date_to': date_to
        })
    
    context = {
        'page_obj': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
        'status_choices': SupplyRequest.STATUS_CHOICES
    }
    
    return render(request, 'requests/request_list.html', context)


@login_required
def request_detail(request, pk):
    """Display detailed view of a supply request"""
    supply_request = get_object_or_404(
        SupplyRequest.objects.select_related('requester', 'approved_by').prefetch_related('items__supply_item'),
        pk=pk
    )
    
    # Check permissions
    if hasattr(request.user, 'userprofile'):
        user_role = request.user.userprofile.role
        if user_role == 'department_user' and supply_request.requester != request.user:
            messages.error(request, 'You can only view your own requests.')
            return redirect('request_list')
    
    context = {
        'request': supply_request,
        'can_edit': supply_request.status == 'pending' and supply_request.requester == request.user,
        'can_approve': hasattr(request.user, 'userprofile') and request.user.userprofile.role in ['gso_staff', 'admin']
    }
    
    return render(request, 'requests/request_detail.html', context)


@login_required
@role_required(['department_user', 'admin'])
def edit_request(request, pk):
    """Edit a pending supply request"""
    supply_request = get_object_or_404(SupplyRequest, pk=pk)
    
    # Check permissions
    if supply_request.requester != request.user:
        messages.error(request, 'You can only edit your own requests.')
        return redirect('request_detail', pk=pk)
    
    if supply_request.status != 'pending':
        messages.error(request, 'Only pending requests can be edited.')
        return redirect('request_detail', pk=pk)
    
    if request.method == 'POST':
        form = SupplyRequestForm(request.POST, instance=supply_request, user=request.user)
        formset = RequestItemInlineFormSet(request.POST, instance=supply_request)
        
        if form.is_valid() and formset.is_valid():
            try:
                with transaction.atomic():
                    form.save()
                    formset.save()
                    
                    messages.success(request, f'Request {supply_request.request_number} updated successfully!')
                    
                    if request.headers.get('HX-Request'):
                        return HttpResponse(
                            f'<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">'
                            f'Request updated successfully!'
                            f'</div>'
                        )
                    
                    return redirect('request_detail', pk=supply_request.pk)
                    
            except Exception as e:
                messages.error(request, f'Error updating request: {str(e)}')
                if request.headers.get('HX-Request'):
                    return HttpResponse(
                        f'<div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">'
                        f'Error updating request: {str(e)}'
                        f'</div>'
                    )
        else:
            if request.headers.get('HX-Request'):
                context = {
                    'form': form,
                    'formset': formset,
                    'request': supply_request,
                    'supply_items': SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
                }
                return render(request, 'requests/partials/edit_form.html', context)
    else:
        form = SupplyRequestForm(instance=supply_request, user=request.user)
        formset = RequestItemInlineFormSet(instance=supply_request)
    
    context = {
        'form': form,
        'formset': formset,
        'request': supply_request,
        'supply_items': SupplyItem.objects.filter(current_stock__gt=0).order_by('name')
    }
    
    return render(request, 'requests/edit_request.html', context)


@login_required
@role_required(['department_user', 'admin'])
def delete_request(request, pk):
    """Delete a pending supply request"""
    supply_request = get_object_or_404(SupplyRequest, pk=pk)
    
    # Check permissions
    if supply_request.requester != request.user:
        messages.error(request, 'You can only delete your own requests.')
        return redirect('request_detail', pk=pk)
    
    if supply_request.status != 'pending':
        messages.error(request, 'Only pending requests can be deleted.')
        return redirect('request_detail', pk=pk)
    
    if request.method == 'POST':
        request_number = supply_request.request_number
        supply_request.delete()
        messages.success(request, f'Request {request_number} deleted successfully!')
        
        if request.headers.get('HX-Request'):
            return HttpResponse(
                '<div hx-swap-oob="true" id="request-deleted">'
                '<div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">'
                f'Request {request_number} deleted successfully!'
                '</div>'
                '</div>'
            )
        
        return redirect('request_list')
    
    context = {'request': supply_request}
    return render(request, 'requests/confirm_delete.html', context)


@login_required
def request_status_update_htmx(request, pk):
    """HTMX endpoint for real-time status updates"""
    supply_request = get_object_or_404(SupplyRequest, pk=pk)
    
    # Check permissions
    if hasattr(request.user, 'userprofile'):
        user_role = request.user.userprofile.role
        if user_role == 'department_user' and supply_request.requester != request.user:
            return HttpResponse('<div class="text-red-500">Access denied</div>')
    
    context = {'request': supply_request}
    return render(request, 'requests/partials/status_badge.html', context)


@login_required
def my_requests_htmx(request):
    """HTMX endpoint for user's recent requests widget"""
    if not hasattr(request.user, 'userprofile') or request.user.userprofile.role != 'department_user':
        return HttpResponse('<div class="text-gray-500">No requests found</div>')
    
    recent_requests = SupplyRequest.objects.filter(
        requester=request.user
    ).order_by('-request_date')[:5]
    
    context = {'recent_requests': recent_requests}
    return render(request, 'requests/partials/my_requests_widget.html', context)