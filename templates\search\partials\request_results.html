<!-- Results Header -->
<div class="bg-white rounded-lg shadow-md mb-4">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                Request Search Results
                {% if total_results %}
                    <span class="text-sm font-normal text-gray-600">({{ total_results }} request{{ total_results|pluralize }})</span>
                {% endif %}
            </h3>
            
            {% if page_obj.has_other_pages %}
                <div class="text-sm text-gray-600">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Results List -->
    <div class="p-6">
        {% if page_obj.object_list %}
            <div class="space-y-4">
                {% for request in page_obj.object_list %}
                    <div class="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors">
                        <!-- Request Header -->
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-1">
                                    <a href="{% url 'request_detail' request.id %}" 
                                       class="text-blue-600 hover:text-blue-800">
                                        {{ request.request_number }}
                                    </a>
                                </h4>
                                <p class="text-sm text-gray-600">
                                    {{ request.requester.get_full_name|default:request.requester.username }} • {{ request.department }}
                                </p>
                            </div>
                            
                            <!-- Status Badge -->
                            <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                {% if request.status == 'pending' %}bg-yellow-100 text-yellow-800
                                {% elif request.status == 'approved' %}bg-green-100 text-green-800
                                {% elif request.status == 'rejected' %}bg-red-100 text-red-800
                                {% elif request.status == 'released' %}bg-purple-100 text-purple-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ request.get_status_display }}
                            </span>
                        </div>
                        
                        <!-- Request Details -->
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Request Date:</span>
                                <p class="font-medium">{{ request.request_date|date:"M d, Y" }}</p>
                            </div>
                            
                            <div>
                                <span class="text-gray-600">Items:</span>
                                <p class="font-medium">{{ request.items.count|default:0 }}</p>
                            </div>
                            
                            {% if request.approved_date %}
                                <div>
                                    <span class="text-gray-600">Approved:</span>
                                    <p class="font-medium">{{ request.approved_date|date:"M d, Y" }}</p>
                                </div>
                            {% endif %}
                        </div>
                        
                        <!-- Request Items Preview -->
                        {% if request.items.all %}
                            <div class="mt-3 pt-3 border-t border-gray-200">
                                <p class="text-xs text-gray-600 mb-2">Items:</p>
                                <div class="flex flex-wrap gap-1">
                                    {% for item in request.items.all|slice:":3" %}
                                        <span class="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                            {{ item.supply_item.name }} ({{ item.quantity_requested }})
                                        </span>
                                    {% endfor %}
                                    {% if request.items.count > 3 %}
                                        <span class="inline-flex px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
                                            +{{ request.items.count|add:"-3" }} more
                                        </span>
                                    {% endif %}
                                </div>
                            </div>
                        {% endif %}
                        
                        <!-- Action Buttons -->
                        <div class="mt-4 flex space-x-2">
                            <a href="{% url 'request_detail' request.id %}" 
                               class="flex-1 text-center px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                                View Details
                            </a>
                            
                            {% if user.userprofile.role == 'admin' or user.userprofile.role == 'gso_staff' %}
                                {% if request.status == 'pending' %}
                                    <button hx-get="{% url 'request_detail_modal_htmx' request.id %}"
                                            hx-target="#modal-container"
                                            hx-swap="innerHTML"
                                            class="px-3 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                                        Review
                                    </button>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No requests found</h3>
                <p class="text-gray-600 mb-4">Try adjusting your search criteria or clearing some filters.</p>
                <a href="{% url 'advanced_request_search' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Clear All Filters
                </a>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                </div>
                
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                        <button hx-get="{% url 'advanced_request_search' %}?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </button>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <button hx-get="{% url 'advanced_request_search' %}?page={{ num }}&{{ request.GET.urlencode }}"
                                    hx-target="#search-results"
                                    hx-indicator="#search-loading"
                                    class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <button hx-get="{% url 'advanced_request_search' %}?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>

<!-- Modal Container -->
<div id="modal-container"></div>
