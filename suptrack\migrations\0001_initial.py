# Generated by Django 5.2.4 on 2025-07-28 09:45

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SupplyCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Supply Category',
                'verbose_name_plural': 'Supply Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='SupplyItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('unit_of_measure', models.CharField(max_length=50)),
                ('current_stock', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('minimum_stock', models.IntegerField(default=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('qr_code', models.ImageField(blank=True, upload_to='qr_codes/')),
                ('qr_code_data', models.CharField(blank=True, max_length=255, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='suptrack.supplycategory')),
            ],
            options={
                'verbose_name': 'Supply Item',
                'verbose_name_plural': 'Supply Items',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='RequestItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_requested', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)])),
                ('quantity_approved', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('quantity_released', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0)])),
                ('supply_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='suptrack.supplyitem')),
            ],
            options={
                'verbose_name': 'Request Item',
                'verbose_name_plural': 'Request Items',
            },
        ),
        migrations.CreateModel(
            name='QRScanLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scan_type', models.CharField(choices=[('issuance', 'Issuance'), ('return', 'Return'), ('inventory_check', 'Inventory Check')], max_length=20)),
                ('scan_datetime', models.DateTimeField(auto_now_add=True)),
                ('location', models.CharField(blank=True, max_length=200)),
                ('notes', models.TextField(blank=True)),
                ('scanned_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('request_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='scan_logs', to='suptrack.requestitem')),
                ('supply_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scan_logs', to='suptrack.supplyitem')),
            ],
            options={
                'verbose_name': 'QR Scan Log',
                'verbose_name_plural': 'QR Scan Logs',
                'ordering': ['-scan_datetime'],
            },
        ),
        migrations.CreateModel(
            name='InventoryTransaction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('transaction_type', models.CharField(choices=[('in', 'Stock In'), ('out', 'Stock Out'), ('adjustment', 'Adjustment')], max_length=20)),
                ('quantity', models.IntegerField()),
                ('previous_stock', models.IntegerField()),
                ('new_stock', models.IntegerField()),
                ('transaction_date', models.DateTimeField(auto_now_add=True)),
                ('reference_number', models.CharField(blank=True, max_length=50)),
                ('notes', models.TextField(blank=True)),
                ('performed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('supply_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='transactions', to='suptrack.supplyitem')),
            ],
            options={
                'verbose_name': 'Inventory Transaction',
                'verbose_name_plural': 'Inventory Transactions',
                'ordering': ['-transaction_date'],
            },
        ),
        migrations.CreateModel(
            name='SupplyRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('request_number', models.CharField(blank=True, max_length=20, unique=True)),
                ('department', models.CharField(max_length=100)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('released', 'Released'), ('rejected', 'Rejected')], default='pending', max_length=20)),
                ('request_date', models.DateTimeField(auto_now_add=True)),
                ('approved_date', models.DateTimeField(blank=True, null=True)),
                ('rejection_reason', models.TextField(blank=True)),
                ('notes', models.TextField(blank=True)),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_requests', to=settings.AUTH_USER_MODEL)),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supply_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Supply Request',
                'verbose_name_plural': 'Supply Requests',
                'ordering': ['-request_date'],
            },
        ),
        migrations.AddField(
            model_name='requestitem',
            name='request',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='suptrack.supplyrequest'),
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('role', models.CharField(choices=[('admin', 'Admin'), ('gso_staff', 'GSO Staff'), ('department_user', 'Department User')], max_length=20)),
                ('department', models.CharField(blank=True, max_length=100)),
                ('phone_number', models.CharField(blank=True, max_length=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'User Profile',
                'verbose_name_plural': 'User Profiles',
            },
        ),
        migrations.AlterUniqueTogether(
            name='requestitem',
            unique_together={('request', 'supply_item')},
        ),
    ]
