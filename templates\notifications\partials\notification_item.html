<div class="p-4 hover:bg-gray-50 transition-colors {% if not notification.is_read %}bg-blue-50{% endif %}">
    <div class="flex items-start space-x-3">
        <!-- Priority/Type Icon -->
        <div class="flex-shrink-0">
            {% if notification.priority == 'urgent' %}
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% elif notification.priority == 'high' %}
                <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-orange-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% elif notification.notification_type == 'request_approved' %}
                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% elif notification.notification_type == 'request_rejected' %}
                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% elif notification.notification_type|slice:":7" == 'request' %}
                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"></path>
                        <path fill-rule="evenodd" d="M4 5a2 2 0 012-2v1a1 1 0 102 0V3h4v1a1 1 0 102 0V3a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% else %}
                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
            {% endif %}
        </div>
        
        <!-- Notification Content -->
        <div class="flex-1 min-w-0">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h4 class="text-sm font-medium text-gray-900 {% if not notification.is_read %}font-semibold{% endif %}">
                        {{ notification.title }}
                        {% if not notification.is_read %}
                            <span class="inline-block w-2 h-2 bg-blue-600 rounded-full ml-2"></span>
                        {% endif %}
                    </h4>
                    <p class="text-sm text-gray-600 mt-1">{{ notification.message }}</p>
                    
                    <!-- Metadata -->
                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>{{ notification.created_at|timesince }} ago</span>
                        
                        {% if notification.priority != 'normal' %}
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium
                                {% if notification.priority == 'urgent' %}bg-red-100 text-red-800
                                {% elif notification.priority == 'high' %}bg-orange-100 text-orange-800
                                {% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ notification.get_priority_display }}
                            </span>
                        {% endif %}
                        
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                            {{ notification.get_notification_type_display }}
                        </span>
                        
                        {% if notification.is_read %}
                            <span class="text-green-600">Read</span>
                        {% endif %}
                    </div>
                    
                    <!-- Action Button -->
                    {% if notification.action_url and notification.action_text %}
                        <div class="mt-3">
                            <a href="{{ notification.action_url }}" 
                               class="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500">
                                {{ notification.action_text }}
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    {% endif %}
                </div>
                
                <!-- Action Buttons -->
                <div class="flex items-center space-x-2 ml-4">
                    {% if not notification.is_read %}
                        <button hx-post="{% url 'mark_notification_read' notification.id %}"
                                hx-target="closest div"
                                hx-swap="outerHTML"
                                class="text-blue-600 hover:text-blue-500 text-sm font-medium">
                            Mark Read
                        </button>
                    {% endif %}
                    
                    <button hx-post="{% url 'dismiss_notification' notification.id %}"
                            hx-target="closest div"
                            hx-swap="outerHTML"
                            class="text-gray-400 hover:text-gray-600">
                        <span class="sr-only">Dismiss</span>
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
