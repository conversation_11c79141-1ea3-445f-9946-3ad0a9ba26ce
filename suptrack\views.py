from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.http import HttpResponseNotFound, HttpResponseServerError, HttpResponseForbidden
from django.template import loader
from django.views.decorators.csrf import requires_csrf_token
import logging

logger = logging.getLogger(__name__)


def home(request):
    """Home view that shows landing page for unauthenticated users or redirects to appropriate dashboard"""
    if request.user.is_authenticated:
        # Check user role and redirect to appropriate dashboard
        try:
            profile = request.user.userprofile
            if profile.role == 'gso_staff':
                return redirect('gso_dashboard_main')
            elif profile.role == 'admin':
                return redirect('admin_dashboard')
            else:
                return redirect('dashboard')
        except:
            # If no profile exists, redirect to default dashboard
            return redirect('dashboard')
    else:
        return render(request, 'landing.html')


@requires_csrf_token
def custom_404_view(request, exception=None):
    """
    Custom 404 error handler with enhanced context
    """
    logger.warning(f"404 Error: {request.path} - User: {request.user}")

    context = {
        'request_path': request.path,
        'user': request.user,
        'exception': str(exception) if exception else None,
    }

    template = loader.get_template('404.html')
    return HttpResponseNotFound(template.render(context, request))


@requires_csrf_token
def custom_500_view(request):
    """
    Custom 500 error handler with enhanced context
    """
    logger.error(f"500 Error: {request.path} - User: {request.user}")

    context = {
        'request_path': request.path,
        'user': request.user,
    }

    template = loader.get_template('500.html')
    return HttpResponseServerError(template.render(context, request))


@requires_csrf_token
def custom_403_view(request, exception=None):
    """
    Custom 403 error handler with enhanced context
    """
    logger.warning(f"403 Error: {request.path} - User: {request.user} - Exception: {exception}")

    context = {
        'request_path': request.path,
        'user': request.user,
        'exception': str(exception) if exception else None,
    }

    template = loader.get_template('403.html')
    return HttpResponseForbidden(template.render(context, request))
