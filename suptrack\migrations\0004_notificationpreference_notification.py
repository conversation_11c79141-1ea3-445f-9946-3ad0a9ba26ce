# Generated by Django 4.2.17 on 2025-07-29 04:32

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('suptrack', '0003_systemconfiguration'),
    ]

    operations = [
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('low_stock_alerts', models.BooleanField(default=True)),
                ('out_of_stock_alerts', models.BooleanField(default=True)),
                ('request_status_updates', models.BooleanField(default=True)),
                ('system_alerts', models.BooleanField(default=True)),
                ('maintenance_notices', models.BooleanField(default=True)),
                ('in_app_notifications', models.BooleanField(default=True)),
                ('email_notifications', models.<PERSON>oleanField(default=False)),
                ('digest_frequency', models.CharField(choices=[('immediate', 'Immediate'), ('hourly', 'Hourly'), ('daily', 'Daily'), ('weekly', 'Weekly')], default='immediate', max_length=20)),
                ('quiet_hours_enabled', models.BooleanField(default=False)),
                ('quiet_hours_start', models.TimeField(blank=True, null=True)),
                ('quiet_hours_end', models.TimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification Preference',
                'verbose_name_plural': 'Notification Preferences',
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('low_stock', 'Low Stock Alert'), ('out_of_stock', 'Out of Stock Alert'), ('request_approved', 'Request Approved'), ('request_rejected', 'Request Rejected'), ('request_released', 'Request Released'), ('request_pending', 'Request Pending Approval'), ('system_alert', 'System Alert'), ('maintenance', 'Maintenance Notice'), ('general', 'General Notification')], max_length=20)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('priority', models.CharField(choices=[('low', 'Low'), ('normal', 'Normal'), ('high', 'High'), ('urgent', 'Urgent')], default='normal', max_length=10)),
                ('is_read', models.BooleanField(default=False)),
                ('is_dismissed', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
                ('action_url', models.URLField(blank=True)),
                ('action_text', models.CharField(blank=True, max_length=50)),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('supply_item', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='suptrack.supplyitem')),
                ('supply_request', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='suptrack.supplyrequest')),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', 'is_read'], name='suptrack_no_recipie_6fe461_idx'), models.Index(fields=['notification_type', 'created_at'], name='suptrack_no_notific_25c2d5_idx'), models.Index(fields=['priority', 'created_at'], name='suptrack_no_priorit_f627fc_idx'), models.Index(fields=['expires_at'], name='suptrack_no_expires_a2ea8a_idx')],
            },
        ),
    ]
