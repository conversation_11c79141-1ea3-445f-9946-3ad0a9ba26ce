#!/usr/bin/env python3
"""
Comprehensive test suite for Smart Supply routing functionality.
Tests all admin sidebar navigation routes, authentication, authorization, and error handling.
"""

import os
import sys
import django
import json

# Setup Django first
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse, NoReverseMatch
from django.http import HttpResponse
from suptrack.models import UserProfile, SupplyItem, SupplyRequest


class ComprehensiveRoutingTest(TestCase):
    """Test suite for comprehensive routing functionality."""
    
    def setUp(self):
        """Set up test data."""
        self.client = Client()
        
        # Create test users
        self.admin_user = User.objects.create_user(
            username='admin_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.admin_user, role='admin')
        
        self.gso_user = User.objects.create_user(
            username='gso_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.gso_user, role='gso_staff')
        
        self.regular_user = User.objects.create_user(
            username='user_test',
            password='testpass123',
            email='<EMAIL>'
        )
        UserProfile.objects.create(user=self.regular_user, role='department_user')
        
        # Create test data
        self.test_item = SupplyItem.objects.create(
            name='Test Item',
            description='Test Description',
            category='office_supplies',
            unit='pieces',
            current_stock=100,
            minimum_stock=10,
            unit_cost=5.00
        )
    
    def test_gso_route_accessibility(self):
        """Test that all GSO routes are accessible to GSO staff."""
        print("\n=== Testing GSO Route Accessibility ===")
        
        # Login as GSO staff
        self.client.login(username='gso_test', password='testpass123')
        
        # Define GSO routes to test
        gso_routes = [
            'gso_dashboard_main',
            'gso_inventory',
            'gso_inventory_add',
            'gso_stock_adjustment',
            'gso_low_stock',
            'gso_inventory_transactions',
            'gso_approvals',
            'gso_approvals_pending',
            'gso_approval_history',
            'gso_requests',
            'gso_qr_scanner',
            'gso_qr_scanner_tool',
            'gso_scan_history',
            'gso_qr_management',
            'gso_qr_list',
            'gso_reports',
            'gso_inventory_report',
            'gso_request_report',
            'gso_usage_report',
            'gso_analytics'
        ]
        
        success_count = 0
        total_count = len(gso_routes)
        
        for route_name in gso_routes:
            try:
                url = reverse(route_name)
                response = self.client.get(url)
                
                if response.status_code in [200, 302]:
                    print(f"✅ {route_name}: {response.status_code}")
                    success_count += 1
                else:
                    print(f"❌ {route_name}: {response.status_code}")
                    
            except NoReverseMatch:
                print(f"❌ {route_name}: URL not found")
            except Exception as e:
                print(f"❌ {route_name}: Error - {e}")
        
        print(f"\nGSO Routes: {success_count}/{total_count} accessible")
        return success_count == total_count
    
    def test_access_control(self):
        """Test access control for different user roles."""
        print("\n=== Testing Access Control ===")
        
        # Test GSO route access for different users
        gso_url = reverse('gso_dashboard_main')
        
        # Test unauthenticated access
        response = self.client.get(gso_url)
        print(f"Unauthenticated access to GSO: {response.status_code}")
        assert response.status_code in [302, 403], "Should redirect or deny unauthenticated users"
        
        # Test regular user access
        self.client.login(username='user_test', password='testpass123')
        response = self.client.get(gso_url)
        print(f"Regular user access to GSO: {response.status_code}")
        assert response.status_code in [302, 403], "Should deny regular users"
        
        # Test GSO staff access
        self.client.login(username='gso_test', password='testpass123')
        response = self.client.get(gso_url)
        print(f"GSO staff access to GSO: {response.status_code}")
        assert response.status_code in [200, 302], "Should allow GSO staff"
        
        print("✅ Access control working correctly")
        return True
    
    def test_error_handling(self):
        """Test error handling for invalid routes and scenarios."""
        print("\n=== Testing Error Handling ===")
        
        # Test 404 handling
        response = self.client.get('/nonexistent-route/')
        print(f"404 handling: {response.status_code}")
        assert response.status_code == 404, "Should return 404 for invalid routes"
        
        # Test 403 handling (access denied)
        self.client.login(username='user_test', password='testpass123')
        gso_url = reverse('gso_dashboard_main')
        response = self.client.get(gso_url)
        print(f"403 handling: {response.status_code}")
        assert response.status_code in [302, 403], "Should handle access denied"
        
        print("✅ Error handling working correctly")
        return True
    
    def test_template_rendering(self):
        """Test that templates render correctly for GSO routes."""
        print("\n=== Testing Template Rendering ===")
        
        self.client.login(username='gso_test', password='testpass123')
        
        # Test key GSO templates
        template_routes = [
            ('gso_dashboard_main', 'GSO Dashboard'),
            ('gso_inventory', 'Inventory'),
            ('gso_approvals', 'Approvals'),
            ('gso_requests', 'Requests'),
            ('gso_qr_scanner', 'QR Scanner'),
            ('gso_reports', 'Reports')
        ]
        
        success_count = 0
        
        for route_name, expected_content in template_routes:
            try:
                url = reverse(route_name)
                response = self.client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    if expected_content.lower() in content.lower():
                        print(f"✅ {route_name}: Template renders correctly")
                        success_count += 1
                    else:
                        print(f"⚠️  {route_name}: Template missing expected content")
                else:
                    print(f"❌ {route_name}: Status {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {route_name}: Error - {e}")
        
        print(f"Template rendering: {success_count}/{len(template_routes)} successful")
        return success_count == len(template_routes)
    
    def test_navigation_state_management(self):
        """Test navigation state management and active states."""
        print("\n=== Testing Navigation State Management ===")
        
        self.client.login(username='gso_test', password='testpass123')
        
        # Test that navigation includes proper data attributes
        response = self.client.get(reverse('gso_dashboard_main'))
        
        if response.status_code == 200:
            content = response.content.decode('utf-8')
            
            # Check for navigation elements
            nav_checks = [
                'data-nav-item' in content,
                'data-nav-section' in content,
                'nav-link' in content,
                'nav-submenu' in content
            ]
            
            passed_checks = sum(nav_checks)
            print(f"Navigation elements: {passed_checks}/{len(nav_checks)} found")
            
            if passed_checks >= 3:
                print("✅ Navigation state management elements present")
                return True
            else:
                print("⚠️  Some navigation elements missing")
                return False
        else:
            print(f"❌ Could not load dashboard: {response.status_code}")
            return False
    
    def test_breadcrumb_generation(self):
        """Test breadcrumb generation for different routes."""
        print("\n=== Testing Breadcrumb Generation ===")
        
        self.client.login(username='gso_test', password='testpass123')
        
        # Test routes that should have breadcrumbs
        breadcrumb_routes = [
            'gso_inventory_add',
            'gso_approvals_pending',
            'gso_qr_scanner_tool',
            'gso_inventory_report'
        ]
        
        success_count = 0
        
        for route_name in breadcrumb_routes:
            try:
                url = reverse(route_name)
                response = self.client.get(url)
                
                if response.status_code == 200:
                    content = response.content.decode('utf-8')
                    if 'breadcrumb' in content.lower() or 'GSO' in content:
                        print(f"✅ {route_name}: Breadcrumbs present")
                        success_count += 1
                    else:
                        print(f"⚠️  {route_name}: Breadcrumbs not found")
                else:
                    print(f"❌ {route_name}: Status {response.status_code}")
                    
            except Exception as e:
                print(f"❌ {route_name}: Error - {e}")
        
        print(f"Breadcrumb generation: {success_count}/{len(breadcrumb_routes)} successful")
        return success_count >= len(breadcrumb_routes) // 2  # At least half should work
    
    def run_all_tests(self):
        """Run all routing tests."""
        print("🚀 Starting Comprehensive Routing Tests")
        print("=" * 50)
        
        test_results = []
        
        # Run all test methods
        test_methods = [
            ('GSO Route Accessibility', self.test_gso_route_accessibility),
            ('Access Control', self.test_access_control),
            ('Error Handling', self.test_error_handling),
            ('Template Rendering', self.test_template_rendering),
            ('Navigation State Management', self.test_navigation_state_management),
            ('Breadcrumb Generation', self.test_breadcrumb_generation)
        ]
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                test_results.append((test_name, result))
            except Exception as e:
                print(f"❌ {test_name}: Exception - {e}")
                test_results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 TEST SUMMARY")
        print("=" * 50)
        
        passed = sum(1 for _, result in test_results if result)
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All routing tests passed!")
            return True
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return False


def main():
    """Main test runner."""
    print("Smart Supply - Comprehensive Routing Test Suite")
    print("=" * 60)
    
    # Create test instance
    test_suite = ComprehensiveRoutingTest()
    test_suite.setUp()
    
    # Run tests
    success = test_suite.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
