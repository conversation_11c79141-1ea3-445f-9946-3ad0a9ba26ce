{% extends 'admin_dashboard.html' %}

{% block title %}System Health - Admin Dashboard{% endblock %}
{% block page_title %}System Health{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Health Overview -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">System Health Overview</h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {% for component, status in health_data.items %}
            <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                    <h4 class="text-sm font-medium text-gray-900">{{ component|title }}</h4>
                    <div class="health-indicator {{ status }}"></div>
                </div>
                <p class="text-xs text-gray-600 mt-1">{{ status|title }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- System Information -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">System Information</h3>
        <p class="text-gray-600">Detailed system monitoring interface will be implemented here.</p>
    </div>
</div>
{% endblock %}
