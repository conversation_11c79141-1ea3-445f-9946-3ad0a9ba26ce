{% extends 'admin_dashboard.html' %}

{% block title %}Notifications Management - Admin Dashboard{% endblock %}
{% block page_title %}Notifications Management{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Total Notifications</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ total_notifications }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="text-sm font-medium text-gray-500 truncate">Unread</dt>
                            <dd class="text-lg font-medium text-gray-900">{{ unread_notifications }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="bg-white shadow rounded-lg p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="{% url 'admin_notifications_list' %}" 
               class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <h4 class="font-medium text-gray-900">View All Notifications</h4>
                <p class="text-sm text-gray-600">Manage all system notifications</p>
            </a>
            
            <a href="{% url 'create_system_notification' %}" 
               class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <h4 class="font-medium text-gray-900">Create Notification</h4>
                <p class="text-sm text-gray-600">Send system-wide notification</p>
            </a>
            
            <a href="{% url 'admin_notification_templates' %}" 
               class="block p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                <h4 class="font-medium text-gray-900">Manage Templates</h4>
                <p class="text-sm text-gray-600">Configure notification templates</p>
            </a>
        </div>
    </div>
</div>
{% endblock %}
