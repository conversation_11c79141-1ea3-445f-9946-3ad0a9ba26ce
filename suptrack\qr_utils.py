"""
QR Code generation and management utilities for supply items
"""
import qrcode
import os
from io import BytesIO
from django.core.files.base import ContentFile
from django.conf import settings
from django.urls import reverse
from PIL import Image, ImageDraw, ImageFont
import uuid


class QRCodeGenerator:
    """Utility class for generating QR codes for supply items"""
    
    def __init__(self):
        self.qr_settings = {
            'version': 1,
            'error_correction': qrcode.constants.ERROR_CORRECT_L,
            'box_size': 10,
            'border': 4,
        }
    
    def generate_qr_code_data(self, supply_item):
        """Generate QR code data string for a supply item"""
        if not supply_item.qr_code_data:
            supply_item.qr_code_data = str(uuid.uuid4())
            supply_item.save()
        return supply_item.qr_code_data
    
    def generate_qr_code_image(self, supply_item, include_label=True):
        """Generate QR code image for a supply item"""
        # Ensure QR code data exists
        qr_data = self.generate_qr_code_data(supply_item)
        
        # Create QR code
        qr = qrcode.QRCode(**self.qr_settings)
        qr.add_data(qr_data)
        qr.make(fit=True)
        
        # Create QR code image
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        if include_label:
            # Add label with item name and ID
            return self._add_label_to_qr_image(qr_img, supply_item)
        
        return qr_img
    
    def _add_label_to_qr_image(self, qr_img, supply_item):
        """Add item name and details as label below QR code"""
        # Convert to RGB if needed
        if qr_img.mode != 'RGB':
            qr_img = qr_img.convert('RGB')
        
        # Calculate dimensions
        qr_width, qr_height = qr_img.size
        label_height = 80
        total_height = qr_height + label_height
        
        # Create new image with space for label
        final_img = Image.new('RGB', (qr_width, total_height), 'white')
        final_img.paste(qr_img, (0, 0))
        
        # Add text label
        draw = ImageDraw.Draw(final_img)
        
        try:
            # Try to use a better font if available
            font_large = ImageFont.truetype("arial.ttf", 16)
            font_small = ImageFont.truetype("arial.ttf", 12)
        except (OSError, IOError):
            # Fallback to default font
            font_large = ImageFont.load_default()
            font_small = ImageFont.load_default()
        
        # Item name (truncate if too long)
        item_name = supply_item.name[:30] + "..." if len(supply_item.name) > 30 else supply_item.name
        
        # Calculate text positions
        name_bbox = draw.textbbox((0, 0), item_name, font=font_large)
        name_width = name_bbox[2] - name_bbox[0]
        name_x = (qr_width - name_width) // 2
        
        # Draw item name
        draw.text((name_x, qr_height + 10), item_name, fill='black', font=font_large)
        
        # Draw additional info
        info_text = f"Stock: {supply_item.current_stock} {supply_item.unit_of_measure}"
        info_bbox = draw.textbbox((0, 0), info_text, font=font_small)
        info_width = info_bbox[2] - info_bbox[0]
        info_x = (qr_width - info_width) // 2
        
        draw.text((info_x, qr_height + 35), info_text, fill='black', font=font_small)
        
        # Draw QR code data (for debugging)
        qr_data_text = f"ID: {supply_item.qr_code_data[:8]}..."
        qr_data_bbox = draw.textbbox((0, 0), qr_data_text, font=font_small)
        qr_data_width = qr_data_bbox[2] - qr_data_bbox[0]
        qr_data_x = (qr_width - qr_data_width) // 2
        
        draw.text((qr_data_x, qr_height + 55), qr_data_text, fill='gray', font=font_small)
        
        return final_img
    
    def save_qr_code_to_supply_item(self, supply_item, include_label=True):
        """Generate and save QR code image to supply item"""
        qr_img = self.generate_qr_code_image(supply_item, include_label)
        
        # Save to BytesIO
        img_buffer = BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        # Create filename
        filename = f"qr_{supply_item.id}_{supply_item.qr_code_data[:8]}.png"
        
        # Save to supply item
        supply_item.qr_code.save(
            filename,
            ContentFile(img_buffer.getvalue()),
            save=True
        )
        
        return supply_item.qr_code.url
    
    def regenerate_qr_code(self, supply_item):
        """Regenerate QR code with new data"""
        # Generate new QR code data
        supply_item.qr_code_data = str(uuid.uuid4())
        
        # Delete old QR code file if exists
        if supply_item.qr_code:
            try:
                supply_item.qr_code.delete(save=False)
            except:
                pass  # File might not exist
        
        # Generate and save new QR code
        return self.save_qr_code_to_supply_item(supply_item)
    
    def batch_generate_qr_codes(self, supply_items, include_label=True):
        """Generate QR codes for multiple supply items"""
        results = []
        
        for supply_item in supply_items:
            try:
                qr_url = self.save_qr_code_to_supply_item(supply_item, include_label)
                results.append({
                    'supply_item': supply_item,
                    'success': True,
                    'qr_url': qr_url,
                    'message': f'QR code generated for {supply_item.name}'
                })
            except Exception as e:
                results.append({
                    'supply_item': supply_item,
                    'success': False,
                    'error': str(e),
                    'message': f'Failed to generate QR code for {supply_item.name}: {str(e)}'
                })
        
        return results


def generate_qr_code_for_item(supply_item, include_label=True):
    """Convenience function to generate QR code for a single item"""
    generator = QRCodeGenerator()
    return generator.save_qr_code_to_supply_item(supply_item, include_label)


def regenerate_qr_code_for_item(supply_item):
    """Convenience function to regenerate QR code for a single item"""
    generator = QRCodeGenerator()
    return generator.regenerate_qr_code(supply_item)


def batch_generate_qr_codes_for_items(supply_items, include_label=True):
    """Convenience function to generate QR codes for multiple items"""
    generator = QRCodeGenerator()
    return generator.batch_generate_qr_codes(supply_items, include_label)
    