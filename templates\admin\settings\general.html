{% extends 'admin_dashboard.html' %}

{% block title %}General Settings - Admin Dashboard{% endblock %}
{% block page_title %}General Settings{% endblock %}

{% block content %}
<div class="bg-white shadow rounded-lg p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">General System Settings</h3>
    
    <form method="post" class="space-y-6">
        {% csrf_token %}
        
        {% for setting in general_settings %}
        <div>
            <label for="setting_{{ setting.key }}" class="block text-sm font-medium text-gray-700">
                {{ setting.key|title }}
            </label>
            <input type="text" 
                   name="setting_{{ setting.key }}" 
                   id="setting_{{ setting.key }}"
                   value="{{ setting.value }}"
                   class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
            {% if setting.description %}
            <p class="mt-1 text-sm text-gray-500">{{ setting.description }}</p>
            {% endif %}
        </div>
        {% endfor %}
        
        <div class="flex justify-end">
            <button type="submit" 
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700">
                Save Settings
            </button>
        </div>
    </form>
</div>
{% endblock %}
