{% extends 'base.html' %}
{% load static %}

{% block title %}Notification Preferences{% endblock %}
{% block page_title %}Notification Preferences{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-6 max-w-4xl">
    <!-- Page Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Notification Preferences</h1>
            <p class="text-gray-600">Customize how and when you receive notifications</p>
        </div>
        <a href="{% url 'notification_center' %}" 
           class="inline-flex items-center px-4 py-2 bg-gray-300 text-gray-700 text-sm font-medium rounded-lg hover:bg-gray-400 transition-colors">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Notifications
        </a>
    </div>

    <!-- Preferences Form -->
    <div class="bg-white rounded-lg shadow">
        <form method="post" 
              hx-post="{% url 'notification_preferences' %}"
              hx-target="#preferences-form"
              hx-swap="outerHTML">
            {% csrf_token %}
            
            <div id="preferences-form">
                {% include 'notifications/partials/preferences_form.html' %}
            </div>
        </form>
    </div>
</div>
{% endblock %}
