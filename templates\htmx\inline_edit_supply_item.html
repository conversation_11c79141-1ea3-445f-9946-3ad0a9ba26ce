<div id="supply-item-{{ supply_item.id }}" class="bg-white border border-gray-200 rounded-lg p-4">
    <form hx-post="{% url 'inline_edit_supply_item' supply_item.id %}" 
          hx-target="#supply-item-{{ supply_item.id }}" 
          hx-swap="outerHTML"
          hx-indicator="#loading-{{ supply_item.id }}"
          class="space-y-4">
        {% csrf_token %}
        
        <!-- Loading indicator -->
        <div id="loading-{{ supply_item.id }}" class="htmx-indicator">
            <div class="flex items-center justify-center py-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span class="ml-2 text-sm text-gray-600">Saving...</span>
            </div>
        </div>
        
        <!-- Form errors -->
        {% if form.non_field_errors %}
            <div class="bg-red-50 border border-red-200 rounded-md p-3">
                <div class="flex">
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                    </svg>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Error</h3>
                        <div class="mt-2 text-sm text-red-700">
                            {{ form.non_field_errors.0 }}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Item Name -->
            <div>
                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Item Name
                </label>
                {{ form.name }}
                {% if form.name.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Category -->
            <div>
                <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Category
                </label>
                {{ form.category }}
                {% if form.category.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.category.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Current Stock -->
            <div>
                <label for="{{ form.current_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Current Stock
                </label>
                {{ form.current_stock }}
                {% if form.current_stock.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.current_stock.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Minimum Stock -->
            <div>
                <label for="{{ form.minimum_stock.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Stock
                </label>
                {{ form.minimum_stock }}
                {% if form.minimum_stock.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.minimum_stock.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Unit of Measure -->
            <div>
                <label for="{{ form.unit_of_measure.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Unit of Measure
                </label>
                {{ form.unit_of_measure }}
                {% if form.unit_of_measure.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.unit_of_measure.errors.0 }}</p>
                {% endif %}
            </div>
            
            <!-- Description -->
            <div>
                <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                    Description
                </label>
                {{ form.description }}
                {% if form.description.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.description.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button type="button" 
                    hx-get="{% url 'supply_item_detail' supply_item.id %}"
                    hx-target="#supply-item-{{ supply_item.id }}"
                    hx-swap="outerHTML"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                Cancel
            </button>
            
            <button type="submit" 
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Save Changes
            </button>
        </div>
    </form>
</div>
