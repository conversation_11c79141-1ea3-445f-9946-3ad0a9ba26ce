#!/usr/bin/env python
"""
Simple test script to validate dashboard functionality
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from suptrack.models import UserProfile

def test_dashboard_views():
    """Test that dashboard views work for different user roles"""
    client = Client()
    
    # Test users
    test_cases = [
        ('admin', 'admin_dashboard'),
        ('gso_staff', 'gso_dashboard'), 
        ('dept_user', 'dashboard')
    ]
    
    for username, expected_template in test_cases:
        try:
            user = User.objects.get(username=username)
            client.force_login(user)
            
            # Test main dashboard
            response = client.get('/dashboard/')
            print(f"✓ {username} dashboard: Status {response.status_code}")
            
            # Test HTMX endpoints
            htmx_endpoints = [
                '/htmx/dashboard-stats/',
                '/htmx/dashboard-activities/', 
                '/htmx/dashboard-widgets/'
            ]
            
            for endpoint in htmx_endpoints:
                response = client.get(endpoint, HTTP_HX_REQUEST='true')
                print(f"✓ {username} {endpoint}: Status {response.status_code}")
                
        except User.DoesNotExist:
            print(f"✗ User {username} does not exist")
        except Exception as e:
            print(f"✗ Error testing {username}: {e}")

if __name__ == '__main__':
    test_dashboard_views()
    print("\nDashboard tests completed!")