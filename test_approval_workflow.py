#!/usr/bin/env python
"""
Test script for the approval workflow system
"""
import os
import sys

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'SmartSupply.settings')

import django
django.setup()

from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from suptrack.models import UserProfile, SupplyCategory, SupplyItem, SupplyRequest, RequestItem

def create_test_data():
    """Create test data for approval workflow testing"""
    print("Creating test data...")
    
    # Create users
    admin_user = User.objects.create_user(
        username='admin_test',
        email='<EMAIL>',
        password='testpass123',
        first_name='Admin',
        last_name='User'
    )
    
    gso_user = User.objects.create_user(
        username='gso_test',
        email='<EMAIL>',
        password='testpass123',
        first_name='GSO',
        last_name='Staff'
    )
    
    dept_user = User.objects.create_user(
        username='dept_test',
        email='<EMAIL>',
        password='testpass123',
        first_name='Department',
        last_name='User'
    )
    
    # Create user profiles
    UserProfile.objects.create(user=admin_user, role='admin', department='Administration')
    UserProfile.objects.create(user=gso_user, role='gso_staff', department='GSO')
    UserProfile.objects.create(user=dept_user, role='department_user', department='IT Department')
    
    # Create supply categories
    office_category, _ = SupplyCategory.objects.get_or_create(
        name='Office Supplies',
        defaults={'description': 'General office supplies and materials'}
    )
    
    tech_category, _ = SupplyCategory.objects.get_or_create(
        name='Technology',
        defaults={'description': 'Computer and technology equipment'}
    )
    
    # Create supply items
    pens, _ = SupplyItem.objects.get_or_create(
        name='Ballpoint Pens',
        defaults={
            'description': 'Blue ink ballpoint pens',
            'category': office_category,
            'unit_of_measure': 'pieces',
            'current_stock': 100,
            'minimum_stock': 20
        }
    )
    
    paper, _ = SupplyItem.objects.get_or_create(
        name='A4 Paper',
        defaults={
            'description': 'White A4 printing paper',
            'category': office_category,
            'unit_of_measure': 'reams',
            'current_stock': 50,
            'minimum_stock': 10
        }
    )
    
    mouse, _ = SupplyItem.objects.get_or_create(
        name='Computer Mouse',
        defaults={
            'description': 'Wireless optical mouse',
            'category': tech_category,
            'unit_of_measure': 'pieces',
            'current_stock': 25,
            'minimum_stock': 5
        }
    )
    
    # Create test supply requests
    request1 = SupplyRequest.objects.create(
        requester=dept_user,
        department='IT Department',
        status='pending',
        notes='Urgent request for office supplies'
    )
    
    RequestItem.objects.create(
        request=request1,
        supply_item=pens,
        quantity_requested=20
    )
    
    RequestItem.objects.create(
        request=request1,
        supply_item=paper,
        quantity_requested=5
    )
    
    request2 = SupplyRequest.objects.create(
        requester=dept_user,
        department='IT Department',
        status='pending',
        notes='Computer equipment needed'
    )
    
    RequestItem.objects.create(
        request=request2,
        supply_item=mouse,
        quantity_requested=3
    )
    
    print(f"Created test data:")
    print(f"- Users: {User.objects.count()}")
    print(f"- Supply Categories: {SupplyCategory.objects.count()}")
    print(f"- Supply Items: {SupplyItem.objects.count()}")
    print(f"- Supply Requests: {SupplyRequest.objects.count()}")
    print(f"- Request Items: {RequestItem.objects.count()}")
    
    return {
        'admin_user': admin_user,
        'gso_user': gso_user,
        'dept_user': dept_user,
        'requests': [request1, request2]
    }

def test_approval_views():
    """Test the approval workflow views"""
    print("\nTesting approval workflow views...")
    
    client = Client()
    test_data = create_test_data()
    
    # Test GSO login
    login_success = client.login(username='gso_test', password='testpass123')
    print(f"GSO login successful: {login_success}")
    
    if not login_success:
        print("Failed to login as GSO user")
        return False
    
    # Test pending requests view
    try:
        response = client.get(reverse('pending_requests_view'))
        print(f"Pending requests view status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Pending requests view accessible")
        else:
            print("✗ Pending requests view failed")
            return False
    except Exception as e:
        print(f"✗ Error accessing pending requests view: {e}")
        return False
    
    # Test approval HTMX endpoint
    try:
        request_id = test_data['requests'][0].id
        response = client.post(reverse('approve_request_htmx', args=[request_id]))
        print(f"Approval HTMX endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Approval HTMX endpoint working")
            
            # Check if request was actually approved
            from suptrack.models import SupplyRequest
            updated_request = SupplyRequest.objects.get(id=request_id)
            if updated_request.status == 'approved':
                print("✓ Request successfully approved")
            else:
                print(f"✗ Request status not updated: {updated_request.status}")
                return False
        else:
            print("✗ Approval HTMX endpoint failed")
            return False
    except Exception as e:
        print(f"✗ Error testing approval endpoint: {e}")
        return False
    
    # Test rejection with reason
    try:
        request_id = test_data['requests'][1].id
        response = client.post(reverse('reject_request_htmx', args=[request_id]), {
            'rejection_reason': 'Insufficient budget for this request'
        })
        print(f"Rejection HTMX endpoint status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Rejection HTMX endpoint working")
            
            # Check if request was actually rejected
            from suptrack.models import SupplyRequest
            updated_request = SupplyRequest.objects.get(id=request_id)
            if updated_request.status == 'rejected':
                print("✓ Request successfully rejected")
                print(f"  Rejection reason: {updated_request.rejection_reason}")
            else:
                print(f"✗ Request status not updated: {updated_request.status}")
                return False
        else:
            print("✗ Rejection HTMX endpoint failed")
            return False
    except Exception as e:
        print(f"✗ Error testing rejection endpoint: {e}")
        return False
    
    # Test approval history view
    try:
        response = client.get(reverse('approval_history_view'))
        print(f"Approval history view status: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ Approval history view accessible")
        else:
            print("✗ Approval history view failed")
            return False
    except Exception as e:
        print(f"✗ Error accessing approval history view: {e}")
        return False
    
    print("\n✓ All approval workflow tests passed!")
    return True

def test_role_based_access():
    """Test role-based access control"""
    print("\nTesting role-based access control...")
    
    client = Client()
    
    # Test department user cannot access approval views
    client.login(username='dept_test', password='testpass123')
    
    try:
        response = client.get(reverse('pending_requests_view'))
        if response.status_code == 302:  # Should redirect
            print("✓ Department user correctly denied access to approval views")
        else:
            print(f"✗ Department user access control failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error testing department user access: {e}")
        return False
    
    # Test admin user can access approval views
    client.login(username='admin_test', password='testpass123')
    
    try:
        response = client.get(reverse('pending_requests_view'))
        if response.status_code == 200:
            print("✓ Admin user can access approval views")
        else:
            print(f"✗ Admin user access failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error testing admin user access: {e}")
        return False
    
    print("✓ Role-based access control working correctly!")
    return True

if __name__ == '__main__':
    print("=== Testing Approval Workflow System ===")
    
    try:
        # Clean up any existing test data
        User.objects.filter(username__endswith='_test').delete()
        
        # Run tests
        approval_tests_passed = test_approval_views()
        access_tests_passed = test_role_based_access()
        
        if approval_tests_passed and access_tests_passed:
            print("\n🎉 All tests passed! Approval workflow system is working correctly.")
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    finally:
        # Clean up test data
        print("\nCleaning up test data...")
        User.objects.filter(username__endswith='_test').delete()
        print("Test data cleaned up.")