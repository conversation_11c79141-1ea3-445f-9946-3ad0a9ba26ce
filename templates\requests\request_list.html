{% extends 'base.html' %}
{% load static %}

{% block title %}Supply Requests{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Supply Requests</h1>
                    <p class="mt-1 text-sm text-gray-600">
                        {% if user.userprofile.role == 'department_user' %}
                            Manage your supply requests
                        {% else %}
                            View and manage all supply requests
                        {% endif %}
                    </p>
                </div>
                {% if user.userprofile.role == 'department_user' or user.userprofile.role == 'admin' %}
                    <a href="{% url 'create_request' %}" 
                       class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        New Request
                    </a>
                {% endif %}
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="p-6">
                <form hx-get="{% url 'request_list' %}" 
                      hx-target="#request-list-container" 
                      hx-trigger="change, submit"
                      class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
                    
                    <div>
                        <label for="search" class="block text-sm font-medium text-gray-700">Search</label>
                        <input type="text" 
                               name="search" 
                               id="search"
                               value="{{ search_query }}"
                               placeholder="Request number, requester, department..."
                               class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
                        <select name="status" 
                                id="status"
                                class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            <option value="">All Statuses</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label for="date_from" class="block text-sm font-medium text-gray-700">From Date</label>
                        <input type="date" 
                               name="date_from" 
                               id="date_from"
                               value="{{ date_from }}"
                               class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                    
                    <div>
                        <label for="date_to" class="block text-sm font-medium text-gray-700">To Date</label>
                        <input type="date" 
                               name="date_to" 
                               id="date_to"
                               value="{{ date_to }}"
                               class="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                    </div>
                </form>
            </div>
        </div>

        <!-- Request List -->
        <div id="request-list-container">
            {% include 'requests/partials/request_list.html' %}
        </div>
    </div>
</div>
{% endblock %}