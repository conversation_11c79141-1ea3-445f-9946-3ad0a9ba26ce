"""
Admin-specific views for the Smart Supply Management System.
These views handle admin-only functionality like user management, system settings, and monitoring.
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib import messages
from django.http import JsonResponse, HttpResponse
from django.core.paginator import Paginator
from django.db.models import Q, Count, Sum, F
from django.utils import timezone
from datetime import timedelta
from .models import (
    UserProfile, SupplyItem, SupplyRequest, InventoryTransaction,
    SystemConfiguration, Notification, QRScanLog
)
from .decorators import role_required
from .error_handlers import handle_view_errors, validate_admin_access
import json


@login_required
@role_required('admin')
@handle_view_errors
def admin_users_list(request):
    """Admin view to list and manage all users"""
    validate_admin_access(request.user)
    
    # Get search and filter parameters
    search = request.GET.get('search', '')
    role_filter = request.GET.get('role', '')
    status_filter = request.GET.get('status', '')
    
    # Build query
    users = User.objects.select_related('userprofile').all()
    
    if search:
        users = users.filter(
            Q(username__icontains=search) |
            Q(first_name__icontains=search) |
            Q(last_name__icontains=search) |
            Q(email__icontains=search)
        )
    
    if role_filter:
        users = users.filter(userprofile__role=role_filter)
    
    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    
    # Pagination
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'search': search,
        'role_filter': role_filter,
        'status_filter': status_filter,
        'total_users': users.count(),
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'admin/users/list.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_users_add(request):
    """Admin view to add new users"""
    validate_admin_access(request.user)
    
    if request.method == 'POST':
        # Handle user creation
        username = request.POST.get('username')
        email = request.POST.get('email')
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        role = request.POST.get('role')
        department = request.POST.get('department', '')
        
        try:
            # Create user
            user = User.objects.create_user(
                username=username,
                email=email,
                first_name=first_name,
                last_name=last_name,
                password='TempPass123!'  # Temporary password
            )
            
            # Create profile
            UserProfile.objects.create(
                user=user,
                role=role,
                department=department
            )
            
            messages.success(request, f'User {username} created successfully!')
            return redirect('admin_users_list')
            
        except Exception as e:
            messages.error(request, f'Error creating user: {str(e)}')
    
    context = {
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'admin/users/add.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_roles_management(request):
    """Admin view to manage user roles and permissions"""
    validate_admin_access(request.user)
    
    # Get role statistics
    role_stats = UserProfile.objects.values('role').annotate(
        count=Count('id')
    ).order_by('role')
    
    context = {
        'role_stats': role_stats,
        'role_choices': UserProfile.ROLE_CHOICES,
    }
    
    return render(request, 'admin/users/roles.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_permissions(request):
    """Admin view to manage user permissions"""
    validate_admin_access(request.user)
    
    context = {
        'permissions_info': 'User permissions management interface'
    }
    
    return render(request, 'admin/users/permissions.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_settings(request):
    """Admin view for system settings overview"""
    validate_admin_access(request.user)
    
    # Get system configuration categories
    settings_by_category = {}
    settings = SystemConfiguration.objects.filter(is_active=True).order_by('category', 'key')
    
    for setting in settings:
        if setting.category not in settings_by_category:
            settings_by_category[setting.category] = []
        settings_by_category[setting.category].append(setting)
    
    context = {
        'settings_by_category': settings_by_category,
    }
    
    return render(request, 'admin/settings/overview.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_settings_general(request):
    """Admin view for general system settings"""
    validate_admin_access(request.user)
    
    if request.method == 'POST':
        # Handle settings update
        for key, value in request.POST.items():
            if key.startswith('setting_'):
                setting_key = key.replace('setting_', '')
                SystemConfiguration.set_setting(
                    setting_key, value, user=request.user
                )
        
        messages.success(request, 'Settings updated successfully!')
        return redirect('admin_settings_general')
    
    # Get general settings
    general_settings = SystemConfiguration.objects.filter(
        category='general', is_active=True
    ).order_by('key')
    
    context = {
        'general_settings': general_settings,
    }
    
    return render(request, 'admin/settings/general.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_settings_email(request):
    """Admin view for email configuration"""
    validate_admin_access(request.user)
    
    context = {
        'email_settings': 'Email configuration interface'
    }
    
    return render(request, 'admin/settings/email.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_settings_backup(request):
    """Admin view for backup settings"""
    validate_admin_access(request.user)
    
    context = {
        'backup_settings': 'Backup configuration interface'
    }
    
    return render(request, 'admin/settings/backup.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_settings_security(request):
    """Admin view for security settings"""
    validate_admin_access(request.user)
    
    context = {
        'security_settings': 'Security configuration interface'
    }
    
    return render(request, 'admin/settings/security.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_inventory(request):
    """Admin view for inventory management"""
    validate_admin_access(request.user)
    
    # Redirect to existing inventory list with admin context
    return redirect('inventory_list')


@login_required
@role_required('admin')
@handle_view_errors
def admin_requests(request):
    """Admin view for request management"""
    validate_admin_access(request.user)
    
    # Get request statistics
    total_requests = SupplyRequest.objects.count()
    pending_requests = SupplyRequest.objects.filter(status='pending').count()
    approved_requests = SupplyRequest.objects.filter(status='approved').count()
    rejected_requests = SupplyRequest.objects.filter(status='rejected').count()
    
    # Recent requests
    recent_requests = SupplyRequest.objects.select_related(
        'user', 'user__userprofile'
    ).order_by('-request_date')[:10]
    
    context = {
        'total_requests': total_requests,
        'pending_requests': pending_requests,
        'approved_requests': approved_requests,
        'rejected_requests': rejected_requests,
        'recent_requests': recent_requests,
    }
    
    return render(request, 'admin/requests/overview.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_bulk_operations(request):
    """Admin view for bulk operations on requests"""
    validate_admin_access(request.user)
    
    context = {
        'bulk_operations': 'Bulk operations interface'
    }
    
    return render(request, 'admin/requests/bulk_operations.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_performance_reports(request):
    """Admin view for system performance reports"""
    validate_admin_access(request.user)
    
    context = {
        'performance_data': 'System performance reports'
    }
    
    return render(request, 'admin/reports/performance.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_audit_logs(request):
    """Admin view for audit logs"""
    validate_admin_access(request.user)
    
    # Get recent QR scan logs as audit data
    recent_scans = QRScanLog.objects.select_related(
        'user', 'supply_item'
    ).order_by('-scan_date')[:50]
    
    context = {
        'recent_scans': recent_scans,
    }
    
    return render(request, 'admin/audit/logs.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_notifications(request):
    """Admin view for notifications management"""
    validate_admin_access(request.user)
    
    # Get notification statistics
    total_notifications = Notification.objects.count()
    unread_notifications = Notification.objects.filter(is_read=False).count()
    
    context = {
        'total_notifications': total_notifications,
        'unread_notifications': unread_notifications,
    }
    
    return render(request, 'admin/notifications/overview.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_notifications_list(request):
    """Admin view to list all notifications"""
    validate_admin_access(request.user)
    
    notifications = Notification.objects.select_related('user').order_by('-created_at')
    
    # Pagination
    paginator = Paginator(notifications, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
    }
    
    return render(request, 'admin/notifications/list.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def create_system_notification(request):
    """Admin view to create system notifications"""
    validate_admin_access(request.user)
    
    if request.method == 'POST':
        title = request.POST.get('title')
        message = request.POST.get('message')
        notification_type = request.POST.get('type', 'info')
        
        # Create notification for all users
        users = User.objects.filter(is_active=True)
        for user in users:
            Notification.objects.create(
                user=user,
                title=title,
                message=message,
                notification_type=notification_type
            )
        
        messages.success(request, f'System notification sent to {users.count()} users!')
        return redirect('admin_notifications')
    
    context = {
        'notification_types': Notification.TYPE_CHOICES,
    }
    
    return render(request, 'admin/notifications/create.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_notification_templates(request):
    """Admin view for notification templates"""
    validate_admin_access(request.user)
    
    context = {
        'templates': 'Notification templates interface'
    }
    
    return render(request, 'admin/notifications/templates.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_notification_settings(request):
    """Admin view for notification settings"""
    validate_admin_access(request.user)
    
    context = {
        'settings': 'Notification settings interface'
    }
    
    return render(request, 'admin/notifications/settings.html', context)


@login_required
@role_required('admin')
@handle_view_errors
def admin_system_health(request):
    """Admin view for system health monitoring"""
    validate_admin_access(request.user)
    
    # Basic system health checks
    health_data = {
        'database': 'healthy',
        'storage': 'healthy',
        'memory': 'healthy',
        'cpu': 'healthy',
    }
    
    context = {
        'health_data': health_data,
    }

    return render(request, 'admin/system/health.html', context)


# HTMX Badge and Indicator Views

@login_required
@role_required('admin')
def admin_users_count(request):
    """HTMX endpoint for user count badge"""
    validate_admin_access(request.user)

    total_users = User.objects.filter(is_active=True).count()
    new_users_today = User.objects.filter(
        date_joined__date=timezone.now().date()
    ).count()

    if new_users_today > 0:
        badge_html = f'<span class="nav-badge info">{new_users_today}</span>'
    else:
        badge_html = ''

    return HttpResponse(badge_html)


@login_required
@role_required('admin')
def admin_inventory_alerts_count(request):
    """HTMX endpoint for inventory alerts badge"""
    validate_admin_access(request.user)

    low_stock_count = SupplyItem.objects.filter(
        current_stock__lte=F('minimum_stock')
    ).count()

    if low_stock_count > 0:
        badge_html = f'<span class="nav-badge warning">{low_stock_count}</span>'
    else:
        badge_html = ''

    return HttpResponse(badge_html)


@login_required
@role_required('admin')
def admin_pending_requests_count(request):
    """HTMX endpoint for pending requests badge"""
    validate_admin_access(request.user)

    pending_count = SupplyRequest.objects.filter(status='pending').count()

    if pending_count > 0:
        badge_html = f'<span class="nav-badge">{pending_count}</span>'
    else:
        badge_html = ''

    return HttpResponse(badge_html)


@login_required
@role_required('admin')
def admin_audit_alerts_count(request):
    """HTMX endpoint for audit alerts badge"""
    validate_admin_access(request.user)

    # Check for recent suspicious activity (example)
    recent_scans = QRScanLog.objects.filter(
        scan_date__gte=timezone.now() - timedelta(hours=1)
    ).count()

    if recent_scans > 10:  # Threshold for "high activity"
        badge_html = f'<span class="nav-badge info">{recent_scans}</span>'
    else:
        badge_html = ''

    return HttpResponse(badge_html)


@login_required
@role_required('admin')
def admin_notification_alerts_count(request):
    """HTMX endpoint for notification alerts badge"""
    validate_admin_access(request.user)

    unread_count = Notification.objects.filter(
        user=request.user, is_read=False
    ).count()

    if unread_count > 0:
        badge_html = f'<span class="nav-badge">{unread_count}</span>'
    else:
        badge_html = ''

    return HttpResponse(badge_html)


@login_required
@role_required('admin')
def admin_system_health_indicator(request):
    """HTMX endpoint for system health indicator"""
    validate_admin_access(request.user)

    # Simple health check - in production this would be more comprehensive
    try:
        # Check database connectivity
        User.objects.count()

        # Check for critical issues
        low_stock_critical = SupplyItem.objects.filter(current_stock=0).count()

        if low_stock_critical > 5:
            status = 'critical'
        elif low_stock_critical > 0:
            status = 'warning'
        else:
            status = 'healthy'

        indicator_html = f'<div class="health-indicator {status}" title="System Status: {status.title()}"></div>'

    except Exception:
        indicator_html = '<div class="health-indicator critical" title="System Status: Critical"></div>'

    return HttpResponse(indicator_html)


@login_required
@role_required('admin')
def admin_quick_stats_htmx(request):
    """HTMX endpoint for admin quick stats"""
    validate_admin_access(request.user)

    try:
        stats = {
            'total_users': User.objects.filter(is_active=True).count(),
            'pending_requests': SupplyRequest.objects.filter(status='pending').count(),
            'low_stock_items': SupplyItem.objects.filter(
                current_stock__lte=F('minimum_stock')
            ).count(),
            'total_items': SupplyItem.objects.count(),
        }

        stats_html = f'''
        <div class="admin-quick-stats">
            <div class="stat-item">
                <span>Active Users</span>
                <span class="stat-value">{stats['total_users']}</span>
            </div>
            <div class="stat-item">
                <span>Pending Requests</span>
                <span class="stat-value">{stats['pending_requests']}</span>
            </div>
            <div class="stat-item">
                <span>Low Stock Items</span>
                <span class="stat-value">{stats['low_stock_items']}</span>
            </div>
            <div class="stat-item">
                <span>Total Items</span>
                <span class="stat-value">{stats['total_items']}</span>
            </div>
        </div>
        '''

    except Exception as e:
        stats_html = f'<div class="text-red-500 text-xs">Error loading stats: {str(e)}</div>'

    return HttpResponse(stats_html)
