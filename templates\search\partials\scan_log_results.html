<!-- Results Header -->
<div class="bg-white rounded-lg shadow-md mb-4">
    <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
            <h3 class="text-lg font-semibold text-gray-900">
                Scan Log Results
                {% if total_results %}
                    <span class="text-sm font-normal text-gray-600">({{ total_results }} scan{{ total_results|pluralize }})</span>
                {% endif %}
            </h3>
            
            {% if page_obj.has_other_pages %}
                <div class="text-sm text-gray-600">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- Results Table -->
    <div class="overflow-x-auto">
        {% if page_obj.object_list %}
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date/Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Notes</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for scan in page_obj.object_list %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ scan.scan_datetime|date:"M d, Y H:i" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="{% url 'supply_item_detail' scan.supply_item.id %}" 
                                   class="text-blue-600 hover:text-blue-800 font-medium">
                                    {{ scan.supply_item.name }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                    {% if scan.scan_type == 'issuance' %}bg-blue-100 text-blue-800
                                    {% elif scan.scan_type == 'return' %}bg-green-100 text-green-800
                                    {% elif scan.scan_type == 'inventory_check' %}bg-purple-100 text-purple-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ scan.get_scan_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ scan.scanned_by.get_full_name|default:scan.scanned_by.username }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ scan.location|default:"-" }}
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ scan.notes|truncatechars:50|default:"-" }}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        {% else %}
            <!-- No Results -->
            <div class="text-center py-12">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V6a1 1 0 00-1-1H5a1 1 0 00-1 1v1a1 1 0 001 1z"></path>
                </svg>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No scan logs found</h3>
                <p class="text-gray-600 mb-4">Try adjusting your search criteria or clearing some filters.</p>
                <a href="{% url 'advanced_scan_log_search' %}" 
                   class="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors">
                    Clear All Filters
                </a>
            </div>
        {% endif %}
    </div>
    
    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-700">
                    Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
                </div>
                
                <div class="flex space-x-1">
                    {% if page_obj.has_previous %}
                        <button hx-get="{% url 'advanced_scan_log_search' %}?page={{ page_obj.previous_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Previous
                        </button>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <span class="px-3 py-2 text-sm bg-blue-600 text-white border border-blue-600 rounded-md">{{ num }}</span>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <button hx-get="{% url 'advanced_scan_log_search' %}?page={{ num }}&{{ request.GET.urlencode }}"
                                    hx-target="#search-results"
                                    hx-indicator="#search-loading"
                                    class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                                {{ num }}
                            </button>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <button hx-get="{% url 'advanced_scan_log_search' %}?page={{ page_obj.next_page_number }}&{{ request.GET.urlencode }}"
                                hx-target="#search-results"
                                hx-indicator="#search-loading"
                                class="px-3 py-2 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                            Next
                        </button>
                    {% endif %}
                </div>
            </div>
        </div>
    {% endif %}
</div>
