<tr class="hover:bg-gray-50" id="request-row-{{ request.id }}">
    <td class="px-6 py-4 whitespace-nowrap">
        {% if show_actions %}
            <input type="checkbox" 
                   class="request-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                   value="{{ request.id }}">
        {% else %}
            <div class="w-4 h-4"></div>
        {% endif %}
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="flex items-center">
            <div>
                <div class="text-sm font-medium text-gray-900">
                    {{ request.request_number }}
                </div>
                <div class="text-sm text-gray-500">
                    {{ request.department }}
                </div>
                <div class="mt-1">
                    {% if request.status == 'pending' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            Pending
                        </span>
                    {% elif request.status == 'approved' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Approved
                        </span>
                    {% elif request.status == 'rejected' %}
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Rejected
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">
            {{ request.requester.get_full_name|default:request.requester.username }}
        </div>
        <div class="text-sm text-gray-500">
            {{ request.requester.email }}
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap">
        <div class="text-sm text-gray-900">
            {{ request.total_items }} item{{ request.total_items|pluralize }}
        </div>
        <div class="text-sm text-gray-500">
            {{ request.total_quantity }} total qty
        </div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
        <div>{{ request.request_date|date:"M d, Y" }}</div>
        <div>{{ request.request_date|time:"g:i A" }}</div>
    </td>
    
    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
        {% if show_actions and request.status == 'pending' %}
            <div class="flex items-center space-x-2">
                <!-- View Details Button -->
                <button hx-get="{% url 'request_detail_modal_htmx' request.id %}"
                        hx-target="#modal-container"
                        hx-swap="innerHTML"
                        class="text-blue-600 hover:text-blue-900 p-1 rounded-full hover:bg-blue-100"
                        title="View Details">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                </button>
                
                <!-- Approve Button -->
                <button hx-post="{% url 'approve_request_htmx' request.id %}"
                        hx-target="#request-row-{{ request.id }}"
                        hx-swap="outerHTML"
                        hx-confirm="Are you sure you want to approve this request?"
                        class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Approve
                </button>
                
                <!-- Reject Button -->
                <button hx-get="{% url 'rejection_modal_htmx' request.id %}"
                        hx-target="#modal-container"
                        hx-swap="innerHTML"
                        class="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Reject
                </button>
            </div>
        {% else %}
            <div class="text-gray-400 text-xs">
                {% if request.status == 'approved' %}
                    Approved by {{ request.approved_by.get_full_name|default:request.approved_by.username }}
                    <br>{{ request.approved_date|date:"M d, Y g:i A" }}
                {% elif request.status == 'rejected' %}
                    Rejected by {{ request.approved_by.get_full_name|default:request.approved_by.username }}
                    <br>{{ request.approved_date|date:"M d, Y g:i A" }}
                {% endif %}
            </div>
        {% endif %}
    </td>
</tr>