<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Sidebar Test - Smart Supply Management</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.13.5/dist/cdn.min.js"></script>
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% load static %}{% static 'css/custom.css' %}">
    
    <style>
        /* Test-specific styles */
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            border: none;
            cursor: pointer;
            margin: 5px;
        }
        
        .test-button:hover {
            background: #2563eb;
        }
        
        .test-results {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .viewport-simulator {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            margin: 20px 0;
            overflow: hidden;
        }
        
        .viewport-controls {
            background: #f1f5f9;
            padding: 10px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .viewport-frame {
            background: white;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-100" x-data="{ 
    sidebarOpen: false,
    testResults: '',
    currentViewport: 'desktop',
    toggleSidebar() {
        this.sidebarOpen = !this.sidebarOpen;
        if (window.innerWidth < 768) {
            document.body.style.overflow = this.sidebarOpen ? 'hidden' : '';
        }
    },
    closeSidebar() {
        this.sidebarOpen = false;
        document.body.style.overflow = '';
    },
    init() {
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 768) {
                this.sidebarOpen = false;
                document.body.style.overflow = '';
            }
        });
    }
}">

    <!-- Mobile menu overlay -->
    <div x-show="sidebarOpen" 
         x-transition:enter="transition-opacity ease-out duration-250"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="mobile-overlay-optimized fixed inset-0 z-40 bg-gray-600 bg-opacity-75 md:hidden"
         :class="sidebarOpen ? 'active' : ''"
         @click="closeSidebar()"
         @touchstart.passive="closeSidebar()">
    </div>

    <!-- Test Sidebar -->
    <div class="custom-sidebar mobile-sidebar-optimized fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg md:translate-x-0"
         :class="sidebarOpen ? 'open translate-x-0' : '-translate-x-full'"
         @click.away="closeSidebar()"
         x-trap.noscroll.inert="sidebarOpen">
        
        <!-- Sidebar header -->
        <div class="flex items-center justify-between h-16 px-4 text-white bg-blue-600">
            <div class="flex items-center">
                <div class="w-8 h-8 bg-blue-700 rounded-lg flex items-center justify-center mr-3">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                </div>
                <div>
                    <h1 class="text-lg font-semibold">Test Sidebar</h1>
                    <p class="text-xs text-blue-200">Mobile Test</p>
                </div>
            </div>
            <button @click="closeSidebar()" 
                    @touchstart.passive="closeSidebar()"
                    class="md:hidden p-2 rounded-md text-white hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-colors duration-200"
                    aria-label="Close sidebar">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <!-- Test Navigation -->
        <nav class="mt-8 px-4 space-y-2">
            <a href="#" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                </svg>
                Dashboard
            </a>
            
            <div class="nav-section" data-nav-section="test">
                <a href="#" class="nav-link flex items-center px-4 py-2 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    Test Section
                    <svg class="w-4 h-4 ml-auto transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </a>
                
                <div class="nav-submenu ml-8 mt-1 space-y-1 hidden">
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                        </svg>
                        Test Item 1
                    </a>
                    <a href="#" class="nav-sublink flex items-center px-4 py-2 text-sm text-gray-600 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                        </svg>
                        Test Item 2
                    </a>
                </div>
            </div>
        </nav>
    </div>

    <!-- Main content -->
    <div class="md:ml-64">
        <!-- Top navigation bar -->
        <div class="bg-white shadow-sm border-b border-gray-200">
            <div class="px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between h-16">
                    <div class="flex items-center">
                        <!-- Mobile menu button -->
                        <button @click="toggleSidebar()"
                                @touchstart.passive="toggleSidebar()"
                                class="mobile-toggle-btn md:hidden"
                                :class="sidebarOpen ? 'active' : ''"
                                :aria-expanded="sidebarOpen"
                                aria-label="Toggle navigation menu"
                                type="button">
                            <svg class="mobile-toggle-icon" 
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>

                        <h2 class="ml-2 text-lg font-semibold text-gray-900">
                            Mobile Sidebar Test
                        </h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Content -->
        <div class="test-container">
            <div class="test-section">
                <h2 class="text-xl font-bold mb-4">Mobile Sidebar Test Suite</h2>
                <p class="text-gray-600 mb-4">
                    This page tests the mobile sidebar functionality across different screen sizes and interactions.
                </p>
                
                <div class="flex flex-wrap gap-2">
                    <button class="test-button" onclick="runMobileSidebarTests()">
                        Run All Tests
                    </button>
                    <button class="test-button" onclick="testToggleButton()">
                        Test Toggle Button
                    </button>
                    <button class="test-button" onclick="testSidebarAnimation()">
                        Test Animations
                    </button>
                    <button class="test-button" onclick="testResponsiveBreakpoints()">
                        Test Breakpoints
                    </button>
                    <button class="test-button" onclick="clearTestResults()">
                        Clear Results
                    </button>
                </div>
                
                <div id="test-results" class="test-results" style="display: none;">
                    <!-- Test results will appear here -->
                </div>
            </div>
            
            <div class="test-section">
                <h3 class="text-lg font-semibold mb-3">Viewport Simulator</h3>
                <div class="viewport-controls">
                    <label class="mr-4">
                        <input type="radio" name="viewport" value="mobile" onchange="setViewport(375, 667)"> 
                        Mobile (375x667)
                    </label>
                    <label class="mr-4">
                        <input type="radio" name="viewport" value="tablet" onchange="setViewport(768, 1024)"> 
                        Tablet (768x1024)
                    </label>
                    <label class="mr-4">
                        <input type="radio" name="viewport" value="desktop" onchange="setViewport(1200, 800)" checked> 
                        Desktop (1200x800)
                    </label>
                </div>
            </div>
            
            <div class="test-section">
                <h3 class="text-lg font-semibold mb-3">Manual Test Instructions</h3>
                <ol class="list-decimal list-inside space-y-2 text-gray-700">
                    <li>Resize your browser window to mobile size (< 768px width)</li>
                    <li>Click the hamburger menu button to open the sidebar</li>
                    <li>Verify the sidebar slides in from the left with smooth animation</li>
                    <li>Check that the overlay appears and clicking it closes the sidebar</li>
                    <li>Test swiping from the left edge to open the sidebar</li>
                    <li>Test swiping left to close the sidebar</li>
                    <li>Verify the Escape key closes the sidebar</li>
                    <li>Check that the sidebar automatically closes when resizing to desktop</li>
                    <li>Ensure all navigation links have proper touch targets (44px minimum)</li>
                    <li>Test with different mobile devices and orientations</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Test Scripts -->
    <script src="{% load static %}{% static 'js/mobile_sidebar.js' %}"></script>
    <script src="{% load static %}{% static 'js/mobile_sidebar_test.js' %}"></script>
    
    <script>
        function runMobileSidebarTests() {
            const tester = new MobileSidebarTester();
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = 'Running tests...\n';
            
            tester.runAllTests().then(() => {
                const report = tester.generateTestReport();
                resultsDiv.innerHTML = `Test completed!\nPassed: ${report.passed}/${report.total}\nSuccess Rate: ${report.successRate.toFixed(1)}%\n\nDetailed Results:\n${tester.testResults.map(r => `${r.passed ? '✅' : '❌'} ${r.test}: ${r.details}`).join('\n')}`;
            });
        }
        
        function testToggleButton() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            const btn = document.querySelector('.mobile-toggle-btn');
            resultsDiv.innerHTML = `Toggle button found: ${!!btn}\nVisible: ${btn ? window.getComputedStyle(btn).display !== 'none' : false}\nSize: ${btn ? `${btn.offsetWidth}x${btn.offsetHeight}` : 'N/A'}`;
        }
        
        function testSidebarAnimation() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            const sidebar = document.querySelector('.custom-sidebar');
            const hasTransition = sidebar ? window.getComputedStyle(sidebar).transition !== 'none' : false;
            resultsDiv.innerHTML = `Sidebar found: ${!!sidebar}\nHas transition: ${hasTransition}\nTransition: ${sidebar ? window.getComputedStyle(sidebar).transition : 'N/A'}`;
        }
        
        function testResponsiveBreakpoints() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = `Current viewport: ${window.innerWidth}x${window.innerHeight}\nIs mobile: ${window.innerWidth < 768}\nToggle button visible: ${document.querySelector('.mobile-toggle-btn') ? window.getComputedStyle(document.querySelector('.mobile-toggle-btn')).display !== 'none' : false}`;
        }
        
        function clearTestResults() {
            document.getElementById('test-results').style.display = 'none';
        }
        
        function setViewport(width, height) {
            // This is for demonstration - in real testing you'd use browser dev tools
            console.log(`Setting viewport to ${width}x${height}`);
        }
    </script>
</body>
</html>
