<!-- Request Form Partial for HTMX -->
<div id="form-container">
    <!-- Basic Request Information -->
    <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
        <div>
            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700">
                Department *
            </label>
            <div class="mt-1">
                {{ form.department }}
                {% if form.department.errors %}
                    <p class="mt-1 text-sm text-red-600">{{ form.department.errors.0 }}</p>
                {% endif %}
            </div>
        </div>
        
        <div>
            <label class="block text-sm font-medium text-gray-700">
                Request Date
            </label>
            <div class="mt-1">
                <input type="text" 
                       value="{% now 'M d, Y' %}" 
                       readonly
                       class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-500 sm:text-sm">
            </div>
        </div>
    </div>

    <div>
        <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700">
            Notes
        </label>
        <div class="mt-1">
            {{ form.notes }}
            {% if form.notes.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
            {% endif %}
        </div>
    </div>

    <!-- Request Items -->
    <div class="border-t pt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Request Items</h3>
        
        <div id="request-items">
            {{ formset.management_form }}
            {% for form in formset %}
                <div class="request-item-form border border-gray-200 rounded-lg p-4 mb-4" data-form-index="{{ forloop.counter0 }}">
                    {% for hidden in form.hidden_fields %}
                        {{ hidden }}
                    {% endfor %}
                    
                    <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                        <div class="sm:col-span-2">
                            <label for="{{ form.supply_item.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                Supply Item *
                            </label>
                            <div class="mt-1">
                                {{ form.supply_item }}
                                {% if form.supply_item.errors %}
                                    <p class="mt-1 text-sm text-red-600">{{ form.supply_item.errors.0 }}</p>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="flex items-end">
                            <div class="flex-1">
                                <label for="{{ form.quantity_requested.id_for_label }}" class="block text-sm font-medium text-gray-700">
                                    Quantity *
                                </label>
                                <div class="mt-1">
                                    {{ form.quantity_requested }}
                                    {% if form.quantity_requested.errors %}
                                        <p class="mt-1 text-sm text-red-600">{{ form.quantity_requested.errors.0 }}</p>
                                    {% endif %}
                                </div>
                            </div>
                            
                            {% if not forloop.first %}
                                <button type="button" 
                                        class="ml-2 inline-flex items-center p-2 border border-transparent rounded-md text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        onclick="removeRequestItem(this)">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if form.DELETE %}
                        {{ form.DELETE }}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        
        <button type="button" 
                id="add-item-btn"
                class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Another Item
        </button>
    </div>

    <!-- Form Errors -->
    {% if formset.non_form_errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {{ formset.non_form_errors }}
        </div>
    {% endif %}

    <!-- Submit Buttons -->
    <div class="flex justify-end space-x-3 pt-6 border-t">
        <a href="{% url 'request_list' %}" 
           class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            Cancel
        </a>
        <button type="submit" 
                class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
            </svg>
            Create Request
        </button>
    </div>
</div>